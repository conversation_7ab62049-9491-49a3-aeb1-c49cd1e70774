#!/usr/bin/env python3
"""
FM Radio Display Test - Force GUI to show
=========================================
"""

import os
import pygame
import sys

def test_display():
    """Test if we can create a display"""
    print("🖥️ Testing display...")
    
    # Force display settings
    os.environ['DISPLAY'] = ':0'
    os.environ['SDL_FBDEV'] = '/dev/fb0'
    
    try:
        pygame.init()
        
        # Try different display modes
        modes_to_try = [
            (480, 320),
            (800, 480),
            (1024, 600)
        ]
        
        for width, height in modes_to_try:
            try:
                print(f"Trying {width}x{height}...")
                screen = pygame.display.set_mode((width, height))
                
                # Test drawing
                screen.fill((0, 100, 0))  # Green background
                
                font = pygame.font.Font(None, 48)
                text = font.render("FM DVR Test", True, (255, 255, 255))
                screen.blit(text, (50, 50))
                
                text2 = font.render("Touch screen to exit", True, (255, 255, 255))
                screen.blit(text2, (50, 150))
                
                pygame.display.flip()
                
                print(f"✅ Display working at {width}x{height}")
                
                # Wait for touch or key
                running = True
                clock = pygame.time.Clock()
                
                while running:
                    for event in pygame.event.get():
                        if event.type == pygame.QUIT:
                            running = False
                        elif event.type == pygame.KEYDOWN:
                            running = False
                        elif event.type == pygame.MOUSEBUTTONDOWN:
                            print("👆 Touch detected!")
                            running = False
                    
                    clock.tick(30)
                
                pygame.quit()
                return True
                
            except Exception as e:
                print(f"❌ Failed {width}x{height}: {e}")
                continue
        
        print("❌ No display mode worked")
        return False
        
    except Exception as e:
        print(f"❌ Display test failed: {e}")
        return False

def main():
    print("🎵 FM Radio Display Test")
    print("=" * 25)
    
    if test_display():
        print("✅ Display is working!")
        print("You can now run the full FM DVR application")
    else:
        print("❌ Display issues detected")
        print("Try running with: sudo python3 fm_radio_display_test.py")

if __name__ == "__main__":
    main()
