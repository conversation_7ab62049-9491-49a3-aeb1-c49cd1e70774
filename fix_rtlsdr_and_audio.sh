#!/bin/bash
# Complete RTL-SDR Blog V4 and Audio Setup Script
# Fixes both RTL-SDR drivers and ALSA audio issues

echo "🔧 RTL-SDR Blog V4 & Audio Fix Script"
echo "====================================="

# Kill any running FM processes first
echo "🛑 Stopping any running FM processes..."
sudo pkill -f rtl_fm 2>/dev/null || true
sudo pkill -f python3.*josh5 2>/dev/null || true
sleep 2

echo ""
echo "🔧 Step 1: Removing old RTL-SDR drivers..."
sudo apt purge -y ^librtlsdr || true
sudo rm -rvf /usr/lib/librtlsdr* /usr/include/rtl-sdr* /usr/local/lib/librtlsdr* /usr/local/include/rtl-sdr* /usr/local/include/rtl_* /usr/local/bin/rtl_* 2>/dev/null || true

echo ""
echo "🔧 Step 2: Installing build dependencies..."
sudo apt-get update
sudo apt-get install -y libusb-1.0-0-dev git cmake pkg-config build-essential

echo ""
echo "🔧 Step 3: Building RTL-SDR Blog drivers..."
cd /tmp
rm -rf rtl-sdr-blog 2>/dev/null || true
git clone https://github.com/rtlsdrblog/rtl-sdr-blog
cd rtl-sdr-blog
mkdir build
cd build
cmake ../ -DINSTALL_UDEV_RULES=ON
make -j$(nproc)
sudo make install

echo ""
echo "🔧 Step 4: Installing udev rules..."
sudo cp ../rtl-sdr.rules /etc/udev/rules.d/
sudo ldconfig

echo ""
echo "🔧 Step 5: Blacklisting DVB-T drivers..."
echo 'blacklist dvb_usb_rtl28xxu' | sudo tee /etc/modprobe.d/blacklist-dvb_usb_rtl28xxu.conf

echo ""
echo "🔧 Step 6: Fixing ALSA configuration..."
cat > /home/<USER>/.asoundrc << 'EOF'
# Fixed ALSA configuration for HifiBerry DAC

pcm.hifiberry {
    type hw
    card 0
    device 0
}

pcm.!default {
    type plug
    slave.pcm "hifiberry"
}

ctl.!default {
    type hw
    card 0
}
EOF

echo "✅ Created fixed ALSA configuration"

echo ""
echo "🔧 Step 7: Testing audio devices..."
echo "Testing HifiBerry DAC:"
if timeout 2 aplay -D hifiberry /dev/zero 2>/dev/null; then
    echo "✅ HifiBerry DAC working"
else
    echo "⚠️ HifiBerry DAC test failed"
fi

echo "Testing default device:"
if timeout 2 aplay -D default /dev/zero 2>/dev/null; then
    echo "✅ Default device working"
else
    echo "⚠️ Default device test failed"
fi

echo ""
echo "🔧 Step 8: Creating improved FM DVR script..."
cat > /home/<USER>/josh5_fixed.py << 'EOF'
#!/usr/bin/env python3
"""
FM Radio DVR - Fixed for RTL-SDR Blog V4 and HifiBerry DAC
==========================================================
"""

import os
import sys
import pygame
import subprocess
import threading
import time
import signal
from pathlib import Path
from collections import deque

# Configuration
FREQUENCIES = [88.1, 92.5, 95.3, 95.9, 97.1, 103.7, 106.1]
SEGMENT_DURATION = 10
MAX_SEGMENTS = 360
SAMPLE_RATE = 48000
PIPE_PATH = "/tmp/fm_radio_pipe"
BUFFER_DIR = "/tmp/fm_dvr"

class FMRadioDVR:
    def __init__(self):
        self.buffer_dir = Path(BUFFER_DIR)
        self.buffer_dir.mkdir(exist_ok=True)
        self.segments = deque(maxlen=MAX_SEGMENTS)
        self.segment_counter = 0
        self.pipe_path = PIPE_PATH
        self.freq_idx = 0
        self.current_freq = FREQUENCIES[self.freq_idx]
        self.is_recording = False
        self.rtl_proc = None
        self.splitter_thread = None
        self.splitter_stop_event = threading.Event()
        self.lock = threading.Lock()

    def _cleanup_resources(self):
        if self.rtl_proc and self.rtl_proc.poll() is None:
            try:
                os.killpg(os.getpgid(self.rtl_proc.pid), signal.SIGTERM)
                self.rtl_proc.wait(timeout=2)
            except (ProcessLookupError, OSError): 
                pass
        self.rtl_proc = None
        if os.path.exists(self.pipe_path):
            try: 
                os.remove(self.pipe_path)
            except OSError: 
                pass

    def start_recording(self, frequency):
        if self.is_recording and self.current_freq == frequency: 
            return
        if self.is_recording: 
            self.stop_recording()
        
        self.current_freq = round(frequency, 1)
        try: 
            self.freq_idx = FREQUENCIES.index(self.current_freq)
        except ValueError: 
            self.freq_idx = -1
        
        if not os.path.exists(self.pipe_path): 
            os.mkfifo(self.pipe_path)
        
        self.splitter_stop_event.clear()
        self.splitter_thread = threading.Thread(target=self._splitter_loop, daemon=True)
        self.splitter_thread.start()
        time.sleep(0.2)
        
        # Fixed RTL-FM command for RTL-SDR Blog V4
        rtl_cmd = (f"rtl_fm -f {self.current_freq}M -s 1200000 -r {SAMPLE_RATE} -g 49.6 -l 0 -A fast -E deemp > {self.pipe_path}")
        
        print(f"🎵 Starting RTL-FM: {rtl_cmd}")
        self.rtl_proc = subprocess.Popen(rtl_cmd, shell=True, preexec_fn=os.setsid)
        self.is_recording = True

    def stop_recording(self):
        if not self.is_recording: 
            return
        self.is_recording = False
        self.splitter_stop_event.set()
        self._cleanup_resources()
        if self.splitter_thread and self.splitter_thread.is_alive():
            self.splitter_thread.join(timeout=2)
        with self.lock:
            for f in list(self.segments):
                try: 
                    f.unlink(missing_ok=True)
                except OSError: 
                    pass
            self.segments.clear()

    def tune(self, step):
        new_freq = self.current_freq + step
        if 87.5 <= new_freq <= 108.0: 
            self.start_recording(new_freq)

    def next_preset(self):
        idx = self.freq_idx if self.freq_idx != -1 else 0
        self.start_recording(FREQUENCIES[(idx + 1) % len(FREQUENCIES)])
        
    def prev_preset(self):
        idx = self.freq_idx if self.freq_idx != -1 else 0
        self.start_recording(FREQUENCIES[(idx - 1 + len(FREQUENCIES)) % len(FREQUENCIES)])

    def _splitter_loop(self):
        segment_bytes = SEGMENT_DURATION * SAMPLE_RATE * 2
        try:
            with open(self.pipe_path, 'rb') as pipe:
                while not self.splitter_stop_event.is_set():
                    data = pipe.read(segment_bytes)
                    if len(data) < segment_bytes: 
                        break
                    if self.splitter_stop_event.is_set(): 
                        break
                    self.segment_counter += 1
                    segment_file = self.buffer_dir / f"segment_{self.segment_counter:05d}.raw"
                    with open(segment_file, 'wb') as f: 
                        f.write(data)
                    with self.lock:
                        if len(self.segments) == self.segments.maxlen:
                            try: 
                                self.segments[0].unlink(missing_ok=True)
                            except OSError: 
                                pass
                        self.segments.append(segment_file)
        except Exception as e:
            print(f"Splitter error: {e}")

    def get_segments(self):
        with self.lock: 
            return list(self.segments)
            
    def current_station(self): 
        return self.current_freq

class SegmentPlayer:
    def __init__(self, dvr):
        self.dvr = dvr
        self.playback_thread = None
        self.playback_proc = None
        self.stop_event = threading.Event()
        self.pause_event = threading.Event()
        self.seek_event = threading.Event()
        self.current_index = 0
        self.is_live = True
        self.is_playing = False
        self.lock = threading.Lock()

    def _start_playback(self):
        self.stop()
        self.stop_event.clear()
        self.pause_event.clear()
        self.is_playing = True
        self.playback_thread = threading.Thread(target=self._play_loop, daemon=True)
        self.playback_thread.start()

    def _play_loop(self):
        # Fixed ALSA command for HifiBerry DAC
        cmd = f"aplay -D default -t raw -r {SAMPLE_RATE} -f S16_LE -c 1 -q -"
        print(f"🔊 Starting playback: {cmd}")
        
        with subprocess.Popen(cmd, shell=True, stdin=subprocess.PIPE, preexec_fn=os.setsid) as self.playback_proc:
            try:
                while not self.stop_event.is_set():
                    self.seek_event.clear()
                    segs = self.dvr.get_segments()
                    with self.lock:
                        if not segs: 
                            time.sleep(0.1)
                            continue
                        if not (0 <= self.current_index < len(segs)):
                            self.current_index = len(segs) - 1 if self.is_live else 0
                        segment_to_play = segs[self.current_index]
                    
                    while not segment_to_play.exists() and not self.stop_event.is_set() and not self.seek_event.is_set():
                        time.sleep(0.05)
                    if self.stop_event.is_set() or self.seek_event.is_set(): 
                        continue
                    
                    with open(segment_to_play, 'rb') as f_in:
                        while not self.stop_event.is_set() and not self.seek_event.is_set():
                            if self.pause_event.is_set(): 
                                time.sleep(0.1)
                                continue
                            chunk = f_in.read(8192)
                            if not chunk: 
                                break
                            try:
                                self.playback_proc.stdin.write(chunk)
                            except BrokenPipeError:
                                print("Audio playback stopped")
                                return
                    
                    with self.lock:
                        if not self.seek_event.is_set():
                            if self.is_live: 
                                self.current_index = len(self.dvr.get_segments()) - 1
                            else:
                                if self.current_index < len(self.dvr.get_segments()) - 1: 
                                    self.current_index += 1
            except Exception as e: 
                print(f"Playback error: {e}")
            finally: 
                self.is_playing = False

    def play_live(self):
        with self.lock:
            self.current_index = len(self.dvr.get_segments()) - 1 if self.dvr.get_segments() else 0
            self.is_live = True
        if self.playback_thread and self.playback_thread.is_alive():
            self.pause_event.clear()
            self.is_playing = True
            self._seek()
        else: 
            self._start_playback()

    def pause(self):
        if self.is_playing: 
            self.pause_event.set()
            self.is_playing = False
            
    def resume(self):
        if not self.is_playing and self.playback_thread and self.playback_thread.is_alive():
            self.pause_event.clear()
            self.is_playing = True
        elif not self.dvr.is_recording: 
            return
        elif not self.playback_thread or not self.playback_thread.is_alive():
            self._start_playback()

    def stop(self):
        self.stop_event.set()
        if self.playback_proc and self.playback_proc.stdin:
            try: 
                self.playback_proc.stdin.close()
            except (OSError, BrokenPipeError): 
                pass
        if self.playback_thread and self.playback_thread.is_alive():
            self.playback_thread.join(timeout=1.0)
        self.is_playing = False

    def _seek(self):
        if self.playback_thread and self.playback_thread.is_alive(): 
            self.seek_event.set()
        else: 
            self._start_playback()

    def skip_backward(self, s=1): 
        self.seek_to_segment(self.current_index - s)
        
    def skip_forward(self, s=1): 
        self.seek_to_segment(self.current_index + s)
        
    def go_live(self): 
        self.play_live()
        
    def seek_to_segment(self, idx):
        with self.lock:
            n = len(self.dvr.get_segments())
            if n == 0: 
                return
            self.current_index = max(0, min(idx, n - 1))
            self.is_live = (self.current_index >= n - 1)
        self._seek()
        
    def get_time_shift(self):
        with self.lock:
            n = len(self.dvr.get_segments())
            if self.is_live or n == 0: 
                return 0
            return (n - 1 - self.current_index) * SEGMENT_DURATION
            
    def is_at_live(self):
        with self.lock: 
            return self.is_live or len(self.dvr.get_segments()) == 0

# Simple GUI for testing
def create_simple_gui():
    os.environ['SDL_VIDEO_CENTERED'] = '1'
    pygame.init()
    screen = pygame.display.set_mode((800, 480))
    pygame.display.set_caption('FM DVR - Fixed')
    font = pygame.font.Font(None, 48)
    return screen, font

def main():
    print("🎵 FM DVR - Fixed Version Starting...")
    
    screen, font = create_simple_gui()
    dvr = FMRadioDVR()
    player = SegmentPlayer(dvr)
    
    # Colors
    BLACK = (0, 0, 0)
    WHITE = (255, 255, 255)
    GREEN = (0, 255, 0)
    RED = (255, 0, 0)
    BLUE = (0, 0, 255)
    
    # Buttons
    play_btn = pygame.Rect(50, 200, 150, 80)
    stop_btn = pygame.Rect(220, 200, 150, 80)
    prev_btn = pygame.Rect(50, 300, 100, 60)
    next_btn = pygame.Rect(170, 300, 100, 60)
    exit_btn = pygame.Rect(650, 400, 100, 60)
    
    running = True
    clock = pygame.time.Clock()
    
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.MOUSEBUTTONDOWN:
                pos = event.pos
                if play_btn.collidepoint(pos):
                    if not dvr.is_recording:
                        dvr.start_recording(dvr.current_station())
                        time.sleep(1)
                        player.play_live()
                    elif player.is_playing:
                        player.pause()
                    else:
                        player.resume()
                elif stop_btn.collidepoint(pos):
                    player.stop()
                    dvr.stop_recording()
                elif prev_btn.collidepoint(pos):
                    dvr.prev_preset()
                    if dvr.is_recording:
                        time.sleep(0.5)
                        player.play_live()
                elif next_btn.collidepoint(pos):
                    dvr.next_preset()
                    if dvr.is_recording:
                        time.sleep(0.5)
                        player.play_live()
                elif exit_btn.collidepoint(pos):
                    running = False
        
        # Draw
        screen.fill(BLACK)
        
        # Frequency
        freq_text = font.render(f"{dvr.current_station():.1f} MHz", True, WHITE)
        screen.blit(freq_text, (300, 50))
        
        # Status
        if dvr.is_recording:
            if player.is_playing:
                status = "PLAYING" if player.is_at_live() else f"SHIFT -{player.get_time_shift()}s"
                color = GREEN if player.is_at_live() else BLUE
            else:
                status = "PAUSED"
                color = RED
        else:
            status = "STOPPED"
            color = RED
            
        status_text = font.render(status, True, color)
        screen.blit(status_text, (300, 120))
        
        # Buttons
        pygame.draw.rect(screen, GREEN if not player.is_playing else RED, play_btn)
        pygame.draw.rect(screen, RED, stop_btn)
        pygame.draw.rect(screen, BLUE, prev_btn)
        pygame.draw.rect(screen, BLUE, next_btn)
        pygame.draw.rect(screen, RED, exit_btn)
        
        # Button labels
        play_text = font.render("PLAY" if not player.is_playing else "PAUSE", True, WHITE)
        screen.blit(play_text, (play_btn.x + 20, play_btn.y + 20))
        
        stop_text = font.render("STOP", True, WHITE)
        screen.blit(stop_text, (stop_btn.x + 40, stop_btn.y + 20))
        
        prev_text = font.render("PREV", True, WHITE)
        screen.blit(prev_text, (prev_btn.x + 10, prev_btn.y + 15))
        
        next_text = font.render("NEXT", True, WHITE)
        screen.blit(next_text, (next_btn.x + 10, next_btn.y + 15))
        
        exit_text = font.render("EXIT", True, WHITE)
        screen.blit(exit_text, (exit_btn.x + 20, exit_btn.y + 15))
        
        pygame.display.flip()
        clock.tick(30)
    
    player.stop()
    dvr.stop_recording()
    pygame.quit()

if __name__ == "__main__":
    main()
EOF

chmod +x /home/<USER>/josh5_fixed.py
chown josh:josh /home/<USER>/josh5_fixed.py

echo ""
echo "🔧 Step 9: Creating new launcher..."
cat > /home/<USER>/launch_fixed_fm.sh << 'EOF'
#!/bin/bash
echo "🎵 Starting Fixed FM DVR..."

# Kill any existing processes
sudo pkill -f rtl_fm 2>/dev/null || true
sudo pkill -f python3.*josh 2>/dev/null || true
sleep 1

# Set display
export DISPLAY=:0

# Clean up
rm -f /tmp/fm_radio_pipe
rm -f /tmp/fm_dvr/segment_*.raw

# Launch fixed version
cd /home/<USER>
python3 josh5_fixed.py
EOF

chmod +x /home/<USER>/launch_fixed_fm.sh
chown josh:josh /home/<USER>/launch_fixed_fm.sh

echo ""
echo "✅ Setup Complete!"
echo "=================="
echo ""
echo "🔄 IMPORTANT: You need to REBOOT for the RTL-SDR drivers to work properly!"
echo ""
echo "After reboot, run: ./launch_fixed_fm.sh"
echo ""
echo "🎯 What was fixed:"
echo "  ✅ RTL-SDR Blog V4 drivers installed"
echo "  ✅ DVB-T drivers blacklisted"
echo "  ✅ ALSA configuration simplified"
echo "  ✅ Audio pipeline fixed for HifiBerry DAC"
echo "  ✅ RTL-FM parameters optimized"
echo ""
echo "🔄 Please reboot now: sudo reboot"
EOF
