#!/usr/bin/env python3
"""
FM DVR GUI - Touch-Friendly Interface for Raspberry Pi
======================================================
- Touch-optimized interface for 5" displays
- Real-time recording status and controls
- File management and playback
- SD card storage with minimal RAM usage
"""

import os
import sys
import time
import threading
import subprocess
import signal
import json
from pathlib import Path
from datetime import datetime, timedelta
import queue
import wave
import struct
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext

# Configuration
SAMPLE_RATE = 48000
CHANNELS = 1
SAMPLE_WIDTH = 2  # 16-bit
BUFFER_SECONDS = 5  # Small RAM buffer (5 seconds = ~500KB)
FILE_DURATION_MINUTES = 30  # Each file is 30 minutes
STORAGE_DIR = Path("/home/<USER>/fm_recordings")
MAX_STORAGE_GB = 20  # Maximum storage usage
DEFAULT_FREQ = 88.1
DEFAULT_GAIN = 30

class FMDVRGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("FM DVR - Digital Radio Recorder")
        self.root.geometry("800x480")  # 5" display resolution
        self.root.configure(bg='#2c3e50')
        
        # DVR backend
        self.dvr = SDCardFMDVR()
        self.recording = False
        self.current_freq = DEFAULT_FREQ
        
        # GUI update thread control
        self.running = True
        
        self.setup_gui()
        self.start_update_thread()
        
        # Handle window close
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_gui(self):
        """Setup the main GUI interface"""
        # Main container
        main_frame = tk.Frame(self.root, bg='#2c3e50')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Title
        title_label = tk.Label(main_frame, text="🎵 FM DVR", 
                              font=('Arial', 24, 'bold'), 
                              fg='#ecf0f1', bg='#2c3e50')
        title_label.pack(pady=(0, 20))
        
        # Status frame
        self.setup_status_frame(main_frame)
        
        # Control frame
        self.setup_control_frame(main_frame)
        
        # Recording list frame
        self.setup_recordings_frame(main_frame)
    
    def setup_status_frame(self, parent):
        """Setup status display frame"""
        status_frame = tk.LabelFrame(parent, text="Status", 
                                   font=('Arial', 14, 'bold'),
                                   fg='#ecf0f1', bg='#34495e', 
                                   relief=tk.RAISED, bd=2)
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Status grid
        status_grid = tk.Frame(status_frame, bg='#34495e')
        status_grid.pack(fill=tk.X, padx=10, pady=10)
        
        # Recording status
        tk.Label(status_grid, text="Recording:", font=('Arial', 12, 'bold'),
                fg='#ecf0f1', bg='#34495e').grid(row=0, column=0, sticky='w', padx=(0, 10))
        self.recording_status = tk.Label(status_grid, text="⏹️ Stopped", 
                                       font=('Arial', 12), fg='#e74c3c', bg='#34495e')
        self.recording_status.grid(row=0, column=1, sticky='w')
        
        # Frequency
        tk.Label(status_grid, text="Frequency:", font=('Arial', 12, 'bold'),
                fg='#ecf0f1', bg='#34495e').grid(row=1, column=0, sticky='w', padx=(0, 10))
        self.freq_status = tk.Label(status_grid, text=f"{DEFAULT_FREQ} MHz", 
                                  font=('Arial', 12), fg='#3498db', bg='#34495e')
        self.freq_status.grid(row=1, column=1, sticky='w')
        
        # Current file
        tk.Label(status_grid, text="Current File:", font=('Arial', 12, 'bold'),
                fg='#ecf0f1', bg='#34495e').grid(row=2, column=0, sticky='w', padx=(0, 10))
        self.file_status = tk.Label(status_grid, text="None", 
                                  font=('Arial', 10), fg='#f39c12', bg='#34495e')
        self.file_status.grid(row=2, column=1, sticky='w')
        
        # Storage info
        tk.Label(status_grid, text="Storage:", font=('Arial', 12, 'bold'),
                fg='#ecf0f1', bg='#34495e').grid(row=3, column=0, sticky='w', padx=(0, 10))
        self.storage_status = tk.Label(status_grid, text="0.0 / 20.0 GB", 
                                     font=('Arial', 12), fg='#27ae60', bg='#34495e')
        self.storage_status.grid(row=3, column=1, sticky='w')
    
    def setup_control_frame(self, parent):
        """Setup control buttons frame"""
        control_frame = tk.LabelFrame(parent, text="Controls", 
                                    font=('Arial', 14, 'bold'),
                                    fg='#ecf0f1', bg='#34495e', 
                                    relief=tk.RAISED, bd=2)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Button container
        btn_container = tk.Frame(control_frame, bg='#34495e')
        btn_container.pack(fill=tk.X, padx=10, pady=10)
        
        # Frequency controls
        freq_frame = tk.Frame(btn_container, bg='#34495e')
        freq_frame.pack(fill=tk.X, pady=(0, 10))
        
        tk.Label(freq_frame, text="Frequency (MHz):", font=('Arial', 12, 'bold'),
                fg='#ecf0f1', bg='#34495e').pack(side=tk.LEFT)
        
        self.freq_var = tk.StringVar(value=str(DEFAULT_FREQ))
        self.freq_entry = tk.Entry(freq_frame, textvariable=self.freq_var, 
                                 font=('Arial', 12), width=8)
        self.freq_entry.pack(side=tk.LEFT, padx=(10, 5))
        
        tk.Button(freq_frame, text="Set", font=('Arial', 10, 'bold'),
                 bg='#3498db', fg='white', relief=tk.RAISED,
                 command=self.set_frequency).pack(side=tk.LEFT, padx=5)
        
        # Main control buttons
        btn_frame = tk.Frame(btn_container, bg='#34495e')
        btn_frame.pack(fill=tk.X)
        
        self.record_btn = tk.Button(btn_frame, text="🔴 Start Recording", 
                                  font=('Arial', 14, 'bold'), bg='#27ae60', fg='white',
                                  relief=tk.RAISED, bd=3, height=2,
                                  command=self.toggle_recording)
        self.record_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        
        tk.Button(btn_frame, text="🔄 Refresh", font=('Arial', 12, 'bold'),
                 bg='#f39c12', fg='white', relief=tk.RAISED, bd=3, height=2,
                 command=self.refresh_recordings).pack(side=tk.LEFT, padx=5)
        
        tk.Button(btn_frame, text="🗑️ Cleanup", font=('Arial', 12, 'bold'),
                 bg='#e74c3c', fg='white', relief=tk.RAISED, bd=3, height=2,
                 command=self.cleanup_files).pack(side=tk.LEFT, padx=(5, 0))
    
    def setup_recordings_frame(self, parent):
        """Setup recordings list frame"""
        recordings_frame = tk.LabelFrame(parent, text="Recordings", 
                                       font=('Arial', 14, 'bold'),
                                       fg='#ecf0f1', bg='#34495e', 
                                       relief=tk.RAISED, bd=2)
        recordings_frame.pack(fill=tk.BOTH, expand=True)
        
        # Recordings listbox with scrollbar
        list_frame = tk.Frame(recordings_frame, bg='#34495e')
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create Treeview for better display
        columns = ('File', 'Size', 'Date')
        self.recordings_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=8)
        
        # Configure columns
        self.recordings_tree.heading('File', text='Filename')
        self.recordings_tree.heading('Size', text='Size (MB)')
        self.recordings_tree.heading('Date', text='Created')
        
        self.recordings_tree.column('File', width=300)
        self.recordings_tree.column('Size', width=100)
        self.recordings_tree.column('Date', width=150)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.recordings_tree.yview)
        self.recordings_tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack treeview and scrollbar
        self.recordings_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Playback button
        tk.Button(recordings_frame, text="▶️ Play Selected", font=('Arial', 12, 'bold'),
                 bg='#9b59b6', fg='white', relief=tk.RAISED, bd=3,
                 command=self.play_selected).pack(pady=5)

    def set_frequency(self):
        """Set new frequency"""
        try:
            freq = float(self.freq_var.get())
            if 87.5 <= freq <= 108.0:
                if self.dvr.set_frequency(freq):
                    self.current_freq = freq
                    messagebox.showinfo("Success", f"Frequency changed to {freq} MHz")
                else:
                    messagebox.showerror("Error", "Failed to set frequency")
            else:
                messagebox.showerror("Error", "Frequency must be between 87.5 and 108.0 MHz")
        except ValueError:
            messagebox.showerror("Error", "Invalid frequency format")

    def toggle_recording(self):
        """Start or stop recording"""
        if not self.recording:
            if self.dvr.start_recording():
                self.recording = True
                self.record_btn.config(text="⏹️ Stop Recording", bg='#e74c3c')
                messagebox.showinfo("Recording", f"Started recording FM {self.current_freq} MHz")
            else:
                messagebox.showerror("Error", "Failed to start recording")
        else:
            self.dvr.stop_recording()
            self.recording = False
            self.record_btn.config(text="🔴 Start Recording", bg='#27ae60')
            messagebox.showinfo("Recording", "Recording stopped")

    def refresh_recordings(self):
        """Refresh the recordings list"""
        self.update_recordings_list()
        messagebox.showinfo("Refresh", "Recordings list updated")

    def cleanup_files(self):
        """Clean up old files"""
        if messagebox.askyesno("Cleanup", "Remove old recordings to free space?"):
            self.dvr.cleanup_old_files()
            self.update_recordings_list()
            messagebox.showinfo("Cleanup", "Old files cleaned up")

    def play_selected(self):
        """Play selected recording"""
        selection = self.recordings_tree.selection()
        if selection:
            item = self.recordings_tree.item(selection[0])
            filename = item['values'][0]
            filepath = STORAGE_DIR / filename

            # Use aplay to play the file
            try:
                subprocess.Popen(['aplay', str(filepath)],
                               stdout=subprocess.DEVNULL,
                               stderr=subprocess.DEVNULL)
                messagebox.showinfo("Playback", f"Playing {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to play file: {e}")
        else:
            messagebox.showwarning("Selection", "Please select a recording to play")

    def update_status(self):
        """Update status display"""
        status = self.dvr.get_status()

        # Recording status
        if status['recording']:
            self.recording_status.config(text="🔴 Recording", fg='#e74c3c')
        else:
            self.recording_status.config(text="⏹️ Stopped", fg='#95a5a6')

        # Frequency
        self.freq_status.config(text=f"{status['frequency']} MHz")

        # Current file
        current_file = status.get('current_file', 'None')
        if current_file and current_file != 'None':
            display_name = current_file.split('/')[-1] if '/' in current_file else current_file
            self.file_status.config(text=display_name)
        else:
            self.file_status.config(text="None")

        # Storage
        storage_text = f"{status['storage_used_gb']:.1f} / {status['storage_limit_gb']} GB"
        self.storage_status.config(text=storage_text)

    def update_recordings_list(self):
        """Update recordings list"""
        # Clear existing items
        for item in self.recordings_tree.get_children():
            self.recordings_tree.delete(item)

        # Get recordings
        recordings = self.dvr.get_recordings()

        # Add recordings to tree
        for rec in recordings:
            self.recordings_tree.insert('', 'end', values=(
                rec['filename'],
                f"{rec['size_mb']:.1f}",
                rec['created']
            ))

    def start_update_thread(self):
        """Start the GUI update thread"""
        def update_loop():
            while self.running:
                try:
                    self.root.after(0, self.update_status)
                    self.root.after(0, self.update_recordings_list)
                    time.sleep(2)  # Update every 2 seconds
                except:
                    break

        update_thread = threading.Thread(target=update_loop, daemon=True)
        update_thread.start()

    def on_closing(self):
        """Handle window closing"""
        if self.recording:
            if messagebox.askyesno("Exit", "Recording is active. Stop recording and exit?"):
                self.dvr.stop_recording()
                self.running = False
                self.root.destroy()
        else:
            self.running = False
            self.root.destroy()


class SDCardFMDVR:
    def __init__(self):
        self.freq = DEFAULT_FREQ
        self.gain = DEFAULT_GAIN
        self.recording = False
        self.rtl_proc = None
        self.current_file = None
        self.current_file_handle = None
        self.file_start_time = None
        self.audio_queue = queue.Queue(maxsize=50)  # Small queue
        self.setup_storage()

    def setup_storage(self):
        """Setup storage directory"""
        STORAGE_DIR.mkdir(exist_ok=True)
        print(f"Storage directory: {STORAGE_DIR}")

    def get_storage_usage(self):
        """Get current storage usage in GB"""
        total_size = 0
        for file_path in STORAGE_DIR.glob("*.wav"):
            total_size += file_path.stat().st_size
        return total_size / (1024**3)  # Convert to GB

    def cleanup_old_files(self):
        """Remove old files if storage limit exceeded"""
        if self.get_storage_usage() > MAX_STORAGE_GB:
            # Get all wav files sorted by creation time
            files = sorted(STORAGE_DIR.glob("*.wav"),
                         key=lambda x: x.stat().st_ctime)

            # Remove oldest files until under limit
            while files and self.get_storage_usage() > MAX_STORAGE_GB * 0.8:
                oldest_file = files.pop(0)
                print(f"Removing old file: {oldest_file.name}")
                oldest_file.unlink()

    def get_new_filename(self):
        """Generate filename for new recording"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return STORAGE_DIR / f"fm_{self.freq}MHz_{timestamp}.wav"

    def start_recording(self):
        """Start FM recording"""
        if self.recording:
            return True

        try:
            # Stop any existing processes
            self.stop_recording()

            # Create new file
            self.current_file = self.get_new_filename()
            self.file_start_time = datetime.now()

            # Start RTL-SDR process
            cmd = [
                'rtl_fm', '-f', f'{self.freq}M', '-s', '48000', '-g', str(self.gain),
                '-A', 'fast', '-r', '48000', '-l', '0', '-E', 'deemp'
            ]

            self.rtl_proc = subprocess.Popen(cmd, stdout=subprocess.PIPE,
                                           stderr=subprocess.DEVNULL)

            # Start audio processing thread
            self.recording = True
            audio_thread = threading.Thread(target=self._audio_processor, daemon=True)
            audio_thread.start()

            print(f"Started new recording: {self.current_file.name}")
            return True

        except Exception as e:
            print(f"Failed to start recording: {e}")
            self.recording = False
            return False

    def stop_recording(self):
        """Stop FM recording"""
        self.recording = False

        if self.rtl_proc:
            try:
                self.rtl_proc.terminate()
                self.rtl_proc.wait(timeout=5)
            except:
                self.rtl_proc.kill()
            self.rtl_proc = None

        if self.current_file_handle:
            self.current_file_handle.close()
            self.current_file_handle = None

        self.current_file = None
        print("Recording stopped")

    def set_frequency(self, freq):
        """Set new frequency"""
        if 87.5 <= freq <= 108.0:
            was_recording = self.recording
            if was_recording:
                self.stop_recording()

            self.freq = freq

            if was_recording:
                return self.start_recording()
            return True
        return False

    def _audio_processor(self):
        """Process audio data from RTL-SDR"""
        try:
            # Setup WAV file
            self.current_file_handle = wave.open(str(self.current_file), 'wb')
            self.current_file_handle.setnchannels(CHANNELS)
            self.current_file_handle.setsampwidth(SAMPLE_WIDTH)
            self.current_file_handle.setframerate(SAMPLE_RATE)

            buffer = b''
            chunk_size = SAMPLE_RATE * SAMPLE_WIDTH * BUFFER_SECONDS  # 5 seconds

            while self.recording and self.rtl_proc and self.rtl_proc.poll() is None:
                # Read data from RTL-SDR
                data = self.rtl_proc.stdout.read(4096)
                if not data:
                    break

                buffer += data

                # Write chunks to file
                while len(buffer) >= chunk_size:
                    chunk = buffer[:chunk_size]
                    buffer = buffer[chunk_size:]

                    # Convert to 16-bit samples
                    samples = struct.unpack(f'{len(chunk)}b', chunk)
                    audio_data = struct.pack(f'{len(samples)}h', *[s * 256 for s in samples])

                    self.current_file_handle.writeframes(audio_data)

                # Check if file duration exceeded
                if datetime.now() - self.file_start_time > timedelta(minutes=FILE_DURATION_MINUTES):
                    self._rotate_file()

                # Cleanup old files if needed
                if self.get_storage_usage() > MAX_STORAGE_GB:
                    self.cleanup_old_files()

        except Exception as e:
            print(f"Audio processing error: {e}")
        finally:
            if self.current_file_handle:
                self.current_file_handle.close()
                self.current_file_handle = None

    def _rotate_file(self):
        """Rotate to new file"""
        if self.current_file_handle:
            self.current_file_handle.close()

        # Start new file
        self.current_file = self.get_new_filename()
        self.file_start_time = datetime.now()
        self.current_file_handle = wave.open(str(self.current_file), 'wb')
        self.current_file_handle.setnchannels(CHANNELS)
        self.current_file_handle.setsampwidth(SAMPLE_WIDTH)
        self.current_file_handle.setframerate(SAMPLE_RATE)

        print(f"Rotated to new file: {self.current_file.name}")

    def get_status(self):
        """Get current status"""
        return {
            'recording': self.recording,
            'frequency': self.freq,
            'current_file': self.current_file.name if self.current_file else None,
            'storage_used_gb': self.get_storage_usage(),
            'storage_limit_gb': MAX_STORAGE_GB,
            'recordings_count': len(list(STORAGE_DIR.glob("*.wav")))
        }

    def get_recordings(self):
        """Get list of recordings"""
        recordings = []
        for file_path in sorted(STORAGE_DIR.glob("*.wav"),
                              key=lambda x: x.stat().st_mtime, reverse=True):
            stat = file_path.stat()
            recordings.append({
                'filename': file_path.name,
                'size_mb': stat.st_size / (1024 * 1024),
                'created': datetime.fromtimestamp(stat.st_ctime).strftime("%Y-%m-%d %H:%M")
            })
        return recordings


def main():
    """Main function"""
    # Create storage directory
    STORAGE_DIR.mkdir(exist_ok=True)

    # Create and run GUI
    root = tk.Tk()
    app = FMDVRGUI(root)

    try:
        root.mainloop()
    except KeyboardInterrupt:
        app.on_closing()

if __name__ == '__main__':
    main()
