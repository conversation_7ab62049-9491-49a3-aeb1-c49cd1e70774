#!/bin/bash
# Launch FM DVR GUI with proper display settings

echo "🎵 Starting FM DVR GUI..."
echo "========================"

# Kill any existing processes
sudo pkill -f rtl_fm 2>/dev/null || true
sudo pkill -f aplay 2>/dev/null || true
sudo pkill -f python3.*fm 2>/dev/null || true
sleep 1

# Set up display environment for touchscreen
export DISPLAY=:0
export SDL_FBDEV=/dev/fb0
export SDL_MOUSEDEV=/dev/input/touchscreen
export SDL_MOUSEDRV=TSLIB

# Check if we're running as the right user
if [ "$USER" != "josh" ]; then
    echo "⚠️ Running as $USER, switching to josh user..."
    sudo -u josh DISPLAY=:0 python3 /home/<USER>/fm_radio_dvr_complete.py
else
    echo "✅ Running as josh user"
    cd /home/<USER>
    python3 fm_radio_dvr_complete.py
fi

echo "👋 FM DVR GUI closed"
