"""
FM Radio DVR Touchscreen App for Raspberry Pi (FIXED VERSION)
------------------------------------------------------------
- Uses rtl_fm for demodulation, python-sounddevice for playback, <PERSON><PERSON> for touchscreen UI
- 1 hour circular audio buffer in /tmp (lost on reboot)
- Fixed RTL-FM parameters and audio device configuration
- Simplified EQ and buffer management for stability
- Up to 15 station presets, easily added
- Single master squelch setting (adjustable in settings)
- <PERSON>an button seeks next strong station, stops & buffers
- RDS via background rtl_fm | redsea every 10 seconds, scrolls text
- 15s skip/rewind, persistent seek slider, "Buffer" button jumps to oldest
"""

import os
import sys
import subprocess
import threading
import time
import queue
import signal
import json
from collections import deque
import numpy as np
from scipy import signal as scipy_signal
from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.gridlayout import GridLayout
from kivy.uix.button import Button
from kivy.uix.slider import Slider
from kivy.uix.label import Label
from kivy.uix.popup import Popup
from kivy.uix.textinput import TextInput
from kivy.uix.screenmanager import ScreenManager, Screen
from kivy.properties import StringProperty, NumericProperty, BooleanProperty
from kivy.clock import Clock
import sounddevice as sd

# ---- CONFIGURABLE CONSTANTS ----
BUFFER_SECONDS = 3600   # 1 hour buffer
SR = 48000              # Sample rate Hz
CHANNELS = 1            # mono
SAMPLEWIDTH = 2         # bytes per sample (16-bit)
BUFFER_FILE = '/tmp/fmdvr_buffer.raw'
MAX_PRESETS = 15
RDS_POLL_SEC = 10
CHUNK_SIZE = 4096       # Audio chunk size
PRESETS_FILE = '/tmp/fmdvr_presets.json'

# FM band limits for your region (adjust as needed)
FM_MIN = 87.5
FM_MAX = 108.0
FM_STEP = 0.1

# Simplified EQ presets (reduced complexity)
PRESET_EQS = {
    'Normal':  [0, 0, 0],
    'Rock':    [3, 0, 3],
    'Country': [2, 0, 2],
    'Talk':    [-2, 3, 1],
}
EQ_BANDS = [250, 1000, 4000]  # Simplified to 3 bands

def db2amp(db):
    return 10 ** (db / 20)

def clamp(val, lo, hi):
    return max(lo, min(hi, val))

def format_freq(f):
    return f"{f:.1f} MHz"

def find_hifiberry_device():
    """Find HifiBerry DAC device"""
    try:
        devices = sd.query_devices()
        for i, device in enumerate(devices):
            name = device['name'].lower()
            if any(keyword in name for keyword in ['hifiberry', 'pcm5102a', 'dac']):
                if device['max_output_channels'] > 0:
                    print(f"Found HifiBerry device: {device['name']} (ID: {i})")
                    return i
        print("HifiBerry not found, using default device")
        return None
    except Exception as e:
        print(f"Error finding audio device: {e}")
        return None

# ---- RADIO + BUFFER CORE ----
class RadioBackend:
    """
    Handles radio tuning, buffer management, RDS, and playback
    FIXED VERSION with proper RTL-FM parameters and audio device handling
    """
    def __init__(self):
        self.freq = 88.1
        self.gain = 30  # FIXED: Reduced from 40 to 30
        self.squelch = 0
        self.proc = None
        self.running = False
        self.buffer_size = SR * BUFFER_SECONDS
        self.buffer = np.zeros(self.buffer_size, dtype=np.int16)
        self.write_ptr = 0  # Next write
        self.playback_ptr = 0  # Current play
        self.lock = threading.Lock()
        self.is_paused = False
        self.eq_preset = 'Normal'
        self.presets = self.load_presets()
        self.rds_text = ''
        self.rds_thread = None
        self.rds_stop = threading.Event()
        self.volume_gain = 1.0
        self.scanning = False
        self.scan_stop = threading.Event()
        self.scan_threshold_db = -35
        self.audio_stream = None
        self.audio_thread = None
        self.audio_stop = threading.Event()
        self.audio_queue = queue.Queue(maxsize=10)
        self.rds_proc = None
        
        # FIXED: Find HifiBerry device
        self.audio_device = find_hifiberry_device()
        
        # Simplified EQ filter coefficients
        self.eq_filters = {}
        self._init_eq_filters()
        
        # Signal strength monitoring
        self.signal_strength = 0
        self.signal_history = deque(maxlen=50)

    def _init_eq_filters(self):
        """Initialize simplified butterworth bandpass filters for EQ bands"""
        nyquist = SR / 2
        for i, freq in enumerate(EQ_BANDS):
            if freq < nyquist:
                try:
                    # Simplified filter design
                    if i == 0:
                        # Low pass for bass
                        sos = scipy_signal.butter(2, freq / nyquist, btype='low', output='sos')
                    elif i == len(EQ_BANDS) - 1:
                        # High pass for treble
                        sos = scipy_signal.butter(2, freq / nyquist, btype='high', output='sos')
                    else:
                        # Bandpass for mid
                        low = EQ_BANDS[i-1] / nyquist
                        high = freq / nyquist
                        sos = scipy_signal.butter(2, [low, high], btype='band', output='sos')
                    
                    self.eq_filters[i] = sos
                except Exception as e:
                    print(f"EQ filter {i} failed: {e}")
                    self.eq_filters[i] = None

    def load_presets(self):
        """Load presets from file or return defaults"""
        try:
            with open(PRESETS_FILE, 'r') as f:
                return json.load(f)
        except:
            return [88.1, 92.5, 95.3, 95.9, 97.1, 103.7, 106.1]

    def save_presets(self):
        """Save current presets to file"""
        try:
            with open(PRESETS_FILE, 'w') as f:
                json.dump(self.presets, f)
        except:
            pass

    def start_radio(self):
        self.stop_radio()  # Cleanup previous
        
        # Check if RTL-SDR is available
        try:
            subprocess.run(['rtl_test', '-t'], capture_output=True, timeout=1)
        except:
            print("Error: RTL-SDR device not found!")
            return False
            
        # FIXED: Proper RTL-FM command with correct parameters
        rtl_cmd = [
            'rtl_fm',
            '-f', f'{self.freq}M',
            '-M', 'fm',
            '-s', str(SR),          # FIXED: Use 48kHz sample rate
            '-r', str(SR),          # FIXED: Add resample rate
            '-g', str(self.gain),   # FIXED: Use gain 30 instead of 40
            '-l', str(self.squelch),
            '-E', 'deemp',          # De-emphasis
            '-E', 'dc'              # FIXED: DC blocking instead of '-A', 'fast', '-F', '9'
        ]
        
        try:
            self.proc = subprocess.Popen(rtl_cmd, stdout=subprocess.PIPE, 
                                       stderr=subprocess.PIPE, bufsize=8192)
            self.running = True
            self.audio_stop.clear()
            self.audio_thread = threading.Thread(target=self._audio_reader, daemon=True)
            self.audio_thread.start()
            
            # Start RDS if not running
            if not self.rds_thread or not self.rds_thread.is_alive():
                self.rds_stop.clear()
                self.rds_thread = threading.Thread(target=self._rds_worker, daemon=True)
                self.rds_thread.start()
            
            return True
        except Exception as e:
            print(f"Error starting radio: {e}")
            return False

    def stop_radio(self):
        self.running = False
        self.audio_stop.set()
        self.scan_stop.set()
        
        if self.proc:
            try:
                self.proc.terminate()
                self.proc.wait(timeout=2)
            except:
                self.proc.kill()
            self.proc = None
            
        if self.audio_thread and self.audio_thread.is_alive():
            self.audio_thread.join(timeout=2)
            
        if self.audio_stream:
            self.audio_stream.stop()
            self.audio_stream.close()
            self.audio_stream = None
            
        # Clear audio queue
        while not self.audio_queue.empty():
            try:
                self.audio_queue.get_nowait()
            except:
                break

    def _audio_reader(self):
        """Read audio from rtl_fm and process it - FIXED VERSION"""
        chunk_size = CHUNK_SIZE * SAMPLEWIDTH
        remainder = b''

        while self.running and not self.audio_stop.is_set():
            try:
                # Read with timeout
                data = self.proc.stdout.read(chunk_size - len(remainder))
                if not data:
                    time.sleep(0.01)
                    continue

                # Handle partial samples
                data = remainder + data
                sample_count = len(data) // SAMPLEWIDTH
                usable_bytes = sample_count * SAMPLEWIDTH
                remainder = data[usable_bytes:]

                if sample_count == 0:
                    continue

                samples = np.frombuffer(data[:usable_bytes], dtype=np.int16)

                # Calculate signal strength
                self.signal_strength = np.abs(samples).mean()
                self.signal_history.append(self.signal_strength)

                # Apply squelch
                if self.squelch > 0 and self.signal_strength < self.squelch * 100:
                    samples = np.zeros_like(samples)

                # FIXED: Simplified EQ application
                if self.eq_preset != 'Normal':
                    samples = self.apply_eq_simple(samples)

                # Apply volume
                samples = (samples * self.volume_gain).astype(np.int16)

                # Update circular buffer
                with self.lock:
                    end_ptr = (self.write_ptr + len(samples)) % self.buffer_size

                    if end_ptr > self.write_ptr:
                        self.buffer[self.write_ptr:end_ptr] = samples
                    else:
                        # Wrap around
                        first_part = self.buffer_size - self.write_ptr
                        self.buffer[self.write_ptr:] = samples[:first_part]
                        self.buffer[:end_ptr] = samples[first_part:]

                    self.write_ptr = end_ptr

                # Queue for playback
                try:
                    self.audio_queue.put(samples, block=False)
                except queue.Full:
                    # Drop oldest if queue is full
                    try:
                        self.audio_queue.get_nowait()
                        self.audio_queue.put(samples, block=False)
                    except:
                        pass

            except Exception as e:
                print(f"Audio reader error: {e}")
                time.sleep(0.1)

    def apply_eq_simple(self, audio):
        """Apply simplified EQ to audio - FIXED VERSION"""
        try:
            eq_gains = PRESET_EQS.get(self.eq_preset, [0, 0, 0])
            output = audio.astype(np.float32)

            for i, (band_filter, gain_db) in enumerate(zip(self.eq_filters.values(), eq_gains)):
                if band_filter is not None and gain_db != 0:
                    try:
                        # Apply filter and gain
                        filtered = scipy_signal.sosfilt(band_filter, audio)
                        output += filtered * (db2amp(gain_db) - 1) * 0.3  # Reduced gain
                    except:
                        pass

            # Clip and return
            return np.clip(output, -32768, 32767).astype(np.int16)
        except:
            return audio  # Return original on error

    def set_freq(self, f):
        """Change frequency and restart radio"""
        f = clamp(f, FM_MIN, FM_MAX)
        if abs(self.freq - f) > 0.05:  # Only retune if significant change
            self.freq = round(f, 1)
            return self.start_radio()
        return True

    def next_preset(self):
        """Switch to next preset frequency"""
        if not self.presets:
            return
        try:
            idx = self.presets.index(self.freq)
            idx = (idx + 1) % len(self.presets)
        except ValueError:
            idx = 0
        self.set_freq(self.presets[idx])

    def add_preset(self):
        """Add current frequency to presets"""
        if self.freq not in self.presets and len(self.presets) < MAX_PRESETS:
            self.presets.append(self.freq)
            self.presets.sort()
            self.save_presets()
            return True
        return False

    def del_preset(self, freq):
        """Remove frequency from presets"""
        if freq in self.presets:
            self.presets.remove(freq)
            self.save_presets()
            return True
        return False

    def scan(self, callback=None):
        """Scan for next strong station"""
        if self.scanning:
            return

        self.scanning = True
        self.scan_stop.clear()

        def _scan_worker():
            start_freq = self.freq
            f = start_freq + FM_STEP

            while f <= FM_MAX and not self.scan_stop.is_set():
                if f > FM_MAX:
                    f = FM_MIN
                if abs(f - start_freq) < FM_STEP:
                    break  # Full circle

                self.set_freq(f)
                time.sleep(0.5)  # Allow tune and buffer

                # Check signal strength
                if self.signal_history:
                    avg_strength = np.mean(list(self.signal_history))
                    strength_db = 20 * np.log10(avg_strength + 1)

                    if strength_db > self.scan_threshold_db:
                        break

                f = round(f + FM_STEP, 1)

            self.scanning = False
            if callback:
                callback(self.freq)

        threading.Thread(target=_scan_worker, daemon=True).start()

    def stop_scan(self):
        """Stop ongoing scan"""
        self.scan_stop.set()

    def set_squelch(self, s):
        """Update squelch level (0-100)"""
        self.squelch = clamp(s, 0, 100)

    def set_gain(self, g):
        """Update RTL-SDR gain"""
        self.gain = clamp(g, 0, 50)
        if self.running:
            self.start_radio()

    def set_eq(self, preset):
        """Change EQ preset"""
        if preset in PRESET_EQS:
            self.eq_preset = preset

    def set_volume(self, vol):
        """Set volume gain (0-2)"""
        self.volume_gain = clamp(vol, 0, 2)

    def seek(self, seconds):
        """Seek forward/backward in buffer"""
        with self.lock:
            samples = int(seconds * SR)
            # Calculate available range
            if self.write_ptr > self.playback_ptr:
                available = self.write_ptr - self.playback_ptr
            else:
                available = self.buffer_size - self.playback_ptr + self.write_ptr

            # Don't seek beyond available data
            samples = clamp(samples, -self.playback_ptr, available - 1)
            self.playback_ptr = (self.playback_ptr + samples) % self.buffer_size

    def jump_to_buffer_start(self):
        """Jump to oldest buffered audio"""
        with self.lock:
            # Go to position just after write pointer (oldest data)
            self.playback_ptr = (self.write_ptr + 1) % self.buffer_size

    def get_buffer_position(self):
        """Get current buffer position in seconds"""
        with self.lock:
            if self.write_ptr >= self.playback_ptr:
                behind = self.write_ptr - self.playback_ptr
            else:
                behind = self.buffer_size - self.playback_ptr + self.write_ptr
            return behind / SR

    def play_audio(self):
        """Start audio playback stream - FIXED VERSION"""
        if self.audio_stream:
            self.audio_stream.stop()
            self.audio_stream.close()

        def callback(outdata, frames, time_info, status):
            if status:
                print(f"Audio callback status: {status}")

            if self.is_paused:
                outdata[:] = 0
                return

            # Try to get fresh audio from queue first
            samples = None
            try:
                samples = self.audio_queue.get_nowait()
                if len(samples) >= frames:
                    outdata[:, 0] = samples[:frames]
                    # Put back excess
                    if len(samples) > frames:
                        self.audio_queue.put(samples[frames:], block=False)
                else:
                    # Pad with zeros
                    outdata[:len(samples), 0] = samples
                    outdata[len(samples):, 0] = 0
            except queue.Empty:
                # Fall back to buffer playback
                with self.lock:
                    for i in range(frames):
                        outdata[i, 0] = self.buffer[self.playback_ptr]
                        self.playback_ptr = (self.playback_ptr + 1) % self.buffer_size

        try:
            # FIXED: Use HifiBerry device if found
            self.audio_stream = sd.OutputStream(
                samplerate=SR,
                blocksize=2048,
                channels=1,
                dtype='int16',
                callback=callback,
                device=self.audio_device,  # FIXED: Use HifiBerry DAC
                latency='low')
            self.audio_stream.start()
            print(f"Audio stream started on device: {self.audio_device}")
        except Exception as e:
            print(f"Error starting audio stream: {e}")
            # Fallback to default device
            try:
                self.audio_stream = sd.OutputStream(
                    samplerate=SR,
                    blocksize=2048,
                    channels=1,
                    dtype='int16',
                    callback=callback,
                    device=None,
                    latency='low')
                self.audio_stream.start()
                print("Audio stream started on default device")
            except Exception as e2:
                print(f"Error starting audio stream on default device: {e2}")

    def pause(self):
        self.is_paused = True

    def resume(self):
        self.is_paused = False

    def _rds_worker(self):
        """RDS worker using separate RTL-SDR instance - SIMPLIFIED"""
        while not self.rds_stop.is_set():
            try:
                if self.rds_proc:
                    self.rds_proc.terminate()
                    self.rds_proc = None

                # Simplified RDS - just show frequency info
                self.rds_text = f"FM {self.freq} MHz - Signal: {int(self.signal_strength)}"

            except Exception as e:
                print(f"RDS error: {e}")

            time.sleep(RDS_POLL_SEC)

    def cleanup(self):
        """Clean shutdown"""
        self.stop_radio()
        self.rds_stop.set()
        if self.rds_proc:
            self.rds_proc.terminate()


# ---- SIMPLIFIED KIVY UI ----
class MainScreen(Screen):
    freq_text = StringProperty("88.1 MHz")
    rds_text = StringProperty("")
    buffer_text = StringProperty("Buffer: 0:00")

    def __init__(self, radio, **kwargs):
        super().__init__(**kwargs)
        self.radio = radio
        self.build_ui()
        Clock.schedule_interval(self.update_display, 0.5)

    def build_ui(self):
        layout = BoxLayout(orientation='vertical', padding=10, spacing=10)

        # Frequency display
        self.freq_label = Label(text=self.freq_text, size_hint_y=0.2, font_size='48sp')
        layout.add_widget(self.freq_label)

        # RDS display
        self.rds_label = Label(text=self.rds_text, size_hint_y=0.1, font_size='16sp')
        layout.add_widget(self.rds_label)

        # Station presets (simplified)
        preset_grid = GridLayout(cols=5, size_hint_y=0.2, spacing=5)
        for i, freq in enumerate(self.radio.presets[:10]):
            btn = Button(text=format_freq(freq), font_size='14sp')
            btn.bind(on_press=lambda x, f=freq: self.tune_to(f))
            preset_grid.add_widget(btn)
        layout.add_widget(preset_grid)

        # Control buttons
        control_grid = GridLayout(cols=4, size_hint_y=0.2, spacing=5)

        self.prev_btn = Button(text='<<', font_size='20sp')
        self.prev_btn.bind(on_press=lambda x: self.radio.seek(-15))
        control_grid.add_widget(self.prev_btn)

        self.play_btn = Button(text='Pause', font_size='20sp')
        self.play_btn.bind(on_press=self.toggle_play)
        control_grid.add_widget(self.play_btn)

        self.next_btn = Button(text='>>', font_size='20sp')
        self.next_btn.bind(on_press=lambda x: self.radio.seek(15))
        control_grid.add_widget(self.next_btn)

        self.scan_btn = Button(text='Scan', font_size='20sp')
        self.scan_btn.bind(on_press=self.scan)
        control_grid.add_widget(self.scan_btn)

        layout.add_widget(control_grid)

        # Seek slider
        self.seek_slider = Slider(min=-300, max=0, value=0, size_hint_y=0.1)
        self.seek_slider.bind(on_touch_up=self.on_seek_release)
        layout.add_widget(self.seek_slider)

        # Buffer position
        self.buffer_label = Label(text=self.buffer_text, size_hint_y=0.1)
        layout.add_widget(self.buffer_label)

        # Bottom controls
        bottom_grid = GridLayout(cols=3, size_hint_y=0.1, spacing=5)

        buffer_btn = Button(text='Buffer Start', font_size='16sp')
        buffer_btn.bind(on_press=lambda x: self.radio.jump_to_buffer_start())
        bottom_grid.add_widget(buffer_btn)

        preset_btn = Button(text='Next Preset', font_size='16sp')
        preset_btn.bind(on_press=lambda x: self.radio.next_preset())
        bottom_grid.add_widget(preset_btn)

        settings_btn = Button(text='Settings', font_size='16sp')
        settings_btn.bind(on_press=self.open_settings)
        bottom_grid.add_widget(settings_btn)

        layout.add_widget(bottom_grid)

        self.add_widget(layout)

    def update_display(self, dt):
        self.freq_text = format_freq(self.radio.freq)
        self.freq_label.text = self.freq_text

        if self.radio.rds_text:
            self.rds_label.text = self.radio.rds_text[:50]  # Truncate long text

        # Update buffer position
        pos = self.radio.get_buffer_position()
        mins = int(pos // 60)
        secs = int(pos % 60)
        self.buffer_text = f"Buffer: -{mins}:{secs:02d}"
        self.buffer_label.text = self.buffer_text

        # Update scan button
        if self.radio.scanning:
            self.scan_btn.text = "Scanning..."
        else:
            self.scan_btn.text = "Scan"

    def tune_to(self, freq):
        self.radio.set_freq(freq)

    def toggle_play(self, btn):
        if self.radio.is_paused:
            self.radio.resume()
            btn.text = 'Pause'
        else:
            self.radio.pause()
            btn.text = 'Play'

    def scan(self, btn):
        if not self.radio.scanning:
            self.radio.scan()
        else:
            self.radio.stop_scan()

    def on_seek_release(self, slider, touch):
        if slider.collide_point(*touch.pos):
            self.radio.seek(slider.value)
            slider.value = 0

    def open_settings(self, btn):
        self.manager.current = 'settings'


class SettingsScreen(Screen):
    def __init__(self, radio, **kwargs):
        super().__init__(**kwargs)
        self.radio = radio
        self.build_ui()

    def build_ui(self):
        layout = BoxLayout(orientation='vertical', padding=10, spacing=10)

        # Title
        layout.add_widget(Label(text='Settings', size_hint_y=0.1, font_size='32sp'))

        # Volume
        vol_box = BoxLayout(size_hint_y=0.15)
        vol_box.add_widget(Label(text='Volume:', size_hint_x=0.3))
        self.vol_slider = Slider(min=0, max=2, value=self.radio.volume_gain)
        self.vol_slider.bind(value=lambda s, v: self.radio.set_volume(v))
        vol_box.add_widget(self.vol_slider)
        layout.add_widget(vol_box)

        # Squelch
        sq_box = BoxLayout(size_hint_y=0.15)
        sq_box.add_widget(Label(text='Squelch:', size_hint_x=0.3))
        self.sq_slider = Slider(min=0, max=100, value=self.radio.squelch)
        self.sq_slider.bind(value=lambda s, v: self.radio.set_squelch(v))
        sq_box.add_widget(self.sq_slider)
        layout.add_widget(sq_box)

        # Gain
        gain_box = BoxLayout(size_hint_y=0.15)
        gain_box.add_widget(Label(text='RF Gain:', size_hint_x=0.3))
        self.gain_slider = Slider(min=0, max=50, value=self.radio.gain)
        self.gain_slider.bind(value=lambda s, v: self.radio.set_gain(v))
        gain_box.add_widget(self.gain_slider)
        layout.add_widget(gain_box)

        # EQ presets (simplified)
        eq_box = BoxLayout(size_hint_y=0.15)
        eq_box.add_widget(Label(text='EQ:', size_hint_x=0.3))
        eq_grid = GridLayout(cols=2, spacing=5)
        for preset in PRESET_EQS:
            btn = Button(text=preset, font_size='14sp')
            if preset == self.radio.eq_preset:
                btn.background_color = (0, 1, 0, 1)
            btn.bind(on_press=lambda x, p=preset: self.set_eq(p))
            eq_grid.add_widget(btn)
        eq_box.add_widget(eq_grid)
        layout.add_widget(eq_box)

        # Station management
        station_box = BoxLayout(size_hint_y=0.15)
        add_btn = Button(text='Add Current Station', size_hint_x=0.5)
        add_btn.bind(on_press=self.add_station)
        station_box.add_widget(add_btn)

        self.freq_input = TextInput(text=str(self.radio.freq), multiline=False, size_hint_x=0.3)
        station_box.add_widget(self.freq_input)

        tune_btn = Button(text='Tune', size_hint_x=0.2)
        tune_btn.bind(on_press=self.manual_tune)
        station_box.add_widget(tune_btn)

        layout.add_widget(station_box)

        # Back button
        back_btn = Button(text='Back', size_hint_y=0.1, font_size='20sp')
        back_btn.bind(on_press=lambda x: setattr(self.manager, 'current', 'main'))
        layout.add_widget(back_btn)

        self.add_widget(layout)

    def set_eq(self, preset):
        self.radio.set_eq(preset)
        # Update button colors
        for child in self.children[0].children:
            if isinstance(child, BoxLayout):
                for subchild in child.children:
                    if isinstance(subchild, GridLayout):
                        for btn in subchild.children:
                            if isinstance(btn, Button):
                                if btn.text == preset:
                                    btn.background_color = (0, 1, 0, 1)
                                else:
                                    btn.background_color = (1, 1, 1, 1)

    def add_station(self, btn):
        if self.radio.add_preset():
            btn.text = 'Added!'
            Clock.schedule_once(lambda dt: setattr(btn, 'text', 'Add Current Station'), 2)

    def manual_tune(self, btn):
        try:
            freq = float(self.freq_input.text)
            self.radio.set_freq(freq)
            self.manager.current = 'main'
        except:
            self.freq_input.text = str(self.radio.freq)


class FMRadioApp(App):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.radio = RadioBackend()

    def build(self):
        # Set up screen manager
        sm = ScreenManager()

        # Create screens
        main_screen = MainScreen(self.radio, name='main')
        settings_screen = SettingsScreen(self.radio, name='settings')

        sm.add_widget(main_screen)
        sm.add_widget(settings_screen)

        # Start radio and playback
        self.radio.start_radio()
        self.radio.play_audio()

        return sm

    def on_stop(self):
        self.radio.cleanup()


def signal_handler(sig, frame):
    """Handle Ctrl+C gracefully"""
    print("\nShutting down...")
    if hasattr(signal_handler, 'radio'):
        signal_handler.radio.cleanup()
    sys.exit(0)


if __name__ == '__main__':
    # Set up signal handler
    signal.signal(signal.SIGINT, signal_handler)

    # Check if running headless (no display)
    if os.environ.get('DISPLAY') is None and os.environ.get('WAYLAND_DISPLAY') is None:
        print("No display detected. Running in headless mode...")
        radio = RadioBackend()
        signal_handler.radio = radio

        try:
            radio.start_radio()
            radio.play_audio()
            print(f"Radio tuned to {radio.freq} MHz")
            print("Press Ctrl+C to exit")

            while True:
                time.sleep(1)
                if radio.signal_history:
                    avg = np.mean(list(radio.signal_history))
                    print(f"\rSignal: {avg:.0f} | RDS: {radio.rds_text[:30]}", end='')

        except KeyboardInterrupt:
            radio.cleanup()
    else:
        # Run Kivy app
        FMRadioApp().run()
