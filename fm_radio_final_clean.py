#!/usr/bin/env python3
"""
FM Radio - FINAL WORKING VERSION
================================
- RTL-SDR Blog V4 drivers
- HifiBerry DAC stereo audio  
- Touch GUI interface
"""

import os
import sys
import pygame
import subprocess
import threading
import time
import signal
from pathlib import Path

# Configuration
FREQUENCIES = [88.1, 92.5, 95.3, 95.9, 97.1, 103.7, 106.1]

class FMRadio:
    def __init__(self):
        self.freq_idx = 0
        self.current_freq = FREQUENCIES[self.freq_idx]
        self.is_playing = False
        self.radio_proc = None
        
    def start_radio(self):
        """Start FM radio with proper stereo audio"""
        self.stop_radio()
        
        # Kill any existing processes
        subprocess.run(['sudo', 'pkill', '-f', 'aplay'], capture_output=True)
        subprocess.run(['sudo', 'pkill', '-f', 'rtl_fm'], capture_output=True)
        subprocess.run(['sudo', 'pkill', '-f', 'sox'], capture_output=True)
        time.sleep(1)
        
        # Fixed command: mono to stereo conversion for HifiBerry DAC
        cmd = f"rtl_fm -f {self.current_freq}M -s 1200000 -r 48000 -g 49.6 -l 0 -A fast -E deemp | sox -t raw -r 48000 -e signed -b 16 -c 1 - -t raw -r 48000 -e signed -b 16 -c 2 - | aplay -D hw:0,0 -r 48000 -f S16_LE -c 2"
        
        print(f"Starting: {cmd}")
        
        try:
            self.radio_proc = subprocess.Popen(cmd, shell=True, preexec_fn=os.setsid)
            self.is_playing = True
            print(f"Radio started on {self.current_freq} MHz")
            return True
        except Exception as e:
            print(f"Error: {e}")
            return False
            
    def stop_radio(self):
        """Stop radio"""
        if self.radio_proc:
            try:
                os.killpg(os.getpgid(self.radio_proc.pid), signal.SIGTERM)
                self.radio_proc.wait(timeout=2)
            except:
                try:
                    os.killpg(os.getpgid(self.radio_proc.pid), signal.SIGKILL)
                except:
                    pass
            self.radio_proc = None
        self.is_playing = False
        print("Radio stopped")
        
    def next_station(self):
        """Next preset"""
        self.freq_idx = (self.freq_idx + 1) % len(FREQUENCIES)
        self.current_freq = FREQUENCIES[self.freq_idx]
        if self.is_playing:
            self.start_radio()
        print(f"Station: {self.current_freq} MHz")
        
    def prev_station(self):
        """Previous preset"""
        self.freq_idx = (self.freq_idx - 1) % len(FREQUENCIES)
        self.current_freq = FREQUENCIES[self.freq_idx]
        if self.is_playing:
            self.start_radio()
        print(f"Station: {self.current_freq} MHz")

def main():
    print("FM Radio - FINAL WORKING VERSION")
    print("================================")
    
    # Set display for touchscreen
    os.environ['DISPLAY'] = ':0'
    
    # Initialize pygame
    pygame.init()
    screen = pygame.display.set_mode((800, 480))
    pygame.display.set_caption('FM Radio - Working!')
    font_large = pygame.font.Font(None, 64)
    font_medium = pygame.font.Font(None, 48)
    font_small = pygame.font.Font(None, 32)
    
    radio = FMRadio()
    
    # Colors
    BLACK = (0, 0, 0)
    WHITE = (255, 255, 255)
    GREEN = (0, 255, 0)
    RED = (255, 0, 0)
    BLUE = (0, 100, 255)
    GRAY = (128, 128, 128)
    
    # Larger buttons for touch
    play_btn = pygame.Rect(50, 200, 150, 80)
    stop_btn = pygame.Rect(220, 200, 150, 80)
    prev_btn = pygame.Rect(50, 300, 120, 60)
    next_btn = pygame.Rect(190, 300, 120, 60)
    exit_btn = pygame.Rect(650, 400, 120, 60)
    
    running = True
    clock = pygame.time.Clock()
    
    print("GUI ready - touch PLAY to start radio")
    print("Available stations:", FREQUENCIES)
    
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    running = False
            elif event.type == pygame.MOUSEBUTTONDOWN:
                pos = event.pos
                if play_btn.collidepoint(pos):
                    print("PLAY touched")
                    radio.start_radio()
                elif stop_btn.collidepoint(pos):
                    print("STOP touched")
                    radio.stop_radio()
                elif prev_btn.collidepoint(pos):
                    print("PREV touched")
                    radio.prev_station()
                elif next_btn.collidepoint(pos):
                    print("NEXT touched")
                    radio.next_station()
                elif exit_btn.collidepoint(pos):
                    print("EXIT touched")
                    running = False
        
        # Draw interface
        screen.fill(BLACK)
        
        # Title
        title = font_large.render("FM RADIO", True, WHITE)
        screen.blit(title, (300, 30))
        
        # Frequency display - large and prominent
        freq_text = font_large.render(f"{radio.current_freq:.1f} MHz", True, WHITE)
        screen.blit(freq_text, (300, 100))
        
        # Status
        status_color = GREEN if radio.is_playing else RED
        status_text = "PLAYING" if radio.is_playing else "STOPPED"
        status = font_medium.render(status_text, True, status_color)
        screen.blit(status, (300, 160))
        
        # Draw buttons with colors
        play_color = GREEN if not radio.is_playing else GRAY
        stop_color = RED if radio.is_playing else GRAY
        
        pygame.draw.rect(screen, play_color, play_btn)
        pygame.draw.rect(screen, stop_color, stop_btn)
        pygame.draw.rect(screen, BLUE, prev_btn)
        pygame.draw.rect(screen, BLUE, next_btn)
        pygame.draw.rect(screen, RED, exit_btn)
        
        # Button borders
        pygame.draw.rect(screen, WHITE, play_btn, 3)
        pygame.draw.rect(screen, WHITE, stop_btn, 3)
        pygame.draw.rect(screen, WHITE, prev_btn, 3)
        pygame.draw.rect(screen, WHITE, next_btn, 3)
        pygame.draw.rect(screen, WHITE, exit_btn, 3)
        
        # Button labels
        play_text = font_medium.render("PLAY", True, WHITE)
        screen.blit(play_text, (play_btn.x + 30, play_btn.y + 25))
        
        stop_text = font_medium.render("STOP", True, WHITE)
        screen.blit(stop_text, (stop_btn.x + 30, stop_btn.y + 25))
        
        prev_text = font_small.render("PREV", True, WHITE)
        screen.blit(prev_text, (prev_btn.x + 25, prev_btn.y + 20))
        
        next_text = font_small.render("NEXT", True, WHITE)
        screen.blit(next_text, (next_btn.x + 25, next_btn.y + 20))
        
        exit_text = font_small.render("EXIT", True, WHITE)
        screen.blit(exit_text, (exit_btn.x + 30, exit_btn.y + 20))
        
        # Instructions
        inst1 = font_small.render("Touch PLAY to start radio", True, WHITE)
        inst2 = font_small.render("PREV/NEXT to change stations", True, WHITE)
        inst3 = font_small.render("Audio: HifiBerry DAC", True, GREEN)
        screen.blit(inst1, (50, 380))
        screen.blit(inst2, (50, 410))
        screen.blit(inst3, (50, 440))
        
        pygame.display.flip()
        clock.tick(30)
    
    radio.stop_radio()
    pygame.quit()
    print("FM Radio closed")

if __name__ == "__main__":
    main()
