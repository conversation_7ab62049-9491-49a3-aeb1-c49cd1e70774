#!/bin/bash
# Simple FM Radio Launcher - WORKING VERSION

echo "🎵 Starting Simple FM Radio - WORKING VERSION..."
echo "================================================"

# Kill any existing processes
pkill -f rtl_fm 2>/dev/null || true
pkill -f python3.*fm 2>/dev/null || true
sleep 2

echo "🎮 Starting Simple FM Radio GUI..."
echo "📻 Frequencies: 88.1, 92.5, 95.3, 95.9, 97.1, 103.7, 106.1 MHz"
echo "🎛️ Controls: PLAY/STOP, PREV/NEXT, RECORD"
echo "📱 Touch-friendly interface for 5\" screen"
echo "✅ WORKING: Single process, no conflicts"
echo ""
echo "Click PLAY to start radio, then use buttons to control"

# Set display for GUI
export DISPLAY=:0

# Launch Simple FM Radio
cd /home/<USER>
python3 ./simple_fm_radio.py
