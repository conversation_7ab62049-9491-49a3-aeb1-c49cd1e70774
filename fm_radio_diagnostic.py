#!/usr/bin/env python3
"""
FM Radio Diagnostic Tool
========================
Checks for conflicts and RTL-SDR availability
"""

import subprocess
import time
import os

def check_usb_devices():
    """Check if RTL-SDR is connected"""
    print("🔍 Checking USB devices...")
    try:
        result = subprocess.run(['lsusb'], capture_output=True, text=True)
        print("USB Devices:")
        for line in result.stdout.split('\n'):
            if line.strip():
                print(f"  {line}")
                if 'RTL' in line or 'Realtek' in line:
                    print("  ✅ RTL-SDR device found!")
                    return True
        print("❌ No RTL-SDR device found")
        return False
    except Exception as e:
        print(f"❌ Error checking USB: {e}")
        return False

def check_running_processes():
    """Check for conflicting processes"""
    print("\n🔍 Checking for running FM radio processes...")
    
    processes = ['rtl_fm', 'rtl_sdr', 'fm_radio', 'simple_fm_radio', 'fm_dvr']
    found_processes = []
    
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        for line in result.stdout.split('\n'):
            for process in processes:
                if process in line and 'grep' not in line:
                    found_processes.append(line.strip())
                    print(f"  ⚠️  Found: {line.strip()}")
        
        if not found_processes:
            print("  ✅ No conflicting processes found")
        return found_processes
    except Exception as e:
        print(f"❌ Error checking processes: {e}")
        return []

def check_systemd_services():
    """Check for systemd services"""
    print("\n🔍 Checking systemd services...")
    
    services = ['fm-radio.service', 'fm_radio.service']
    
    for service in services:
        try:
            result = subprocess.run(['systemctl', 'is-active', service], 
                                  capture_output=True, text=True)
            if result.stdout.strip() == 'active':
                print(f"  ⚠️  Service {service} is running")
            else:
                print(f"  ✅ Service {service} is not active")
        except:
            print(f"  ✅ Service {service} not found")

def test_rtl_device():
    """Test RTL-SDR device"""
    print("\n🔍 Testing RTL-SDR device...")
    
    try:
        print("  Running rtl_test...")
        result = subprocess.run(['rtl_test', '-t'], capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0:
            print("  ✅ RTL-SDR device test passed")
            return True
        else:
            print("  ❌ RTL-SDR device test failed")
            print(f"  Error: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("  ❌ RTL-SDR test timed out (device may be in use)")
        return False
    except FileNotFoundError:
        print("  ❌ rtl_test command not found (RTL-SDR tools not installed?)")
        return False
    except Exception as e:
        print(f"  ❌ Error testing device: {e}")
        return False

def check_audio_system():
    """Check audio system"""
    print("\n🔍 Checking audio system...")
    
    try:
        result = subprocess.run(['aplay', '-l'], capture_output=True, text=True)
        print("  Audio devices:")
        for line in result.stdout.split('\n'):
            if 'card' in line:
                print(f"    {line.strip()}")
        
        # Test if aplay works
        result = subprocess.run(['which', 'aplay'], capture_output=True)
        if result.returncode == 0:
            print("  ✅ aplay command available")
        else:
            print("  ❌ aplay command not found")
            
    except Exception as e:
        print(f"  ❌ Error checking audio: {e}")

def kill_conflicting_processes():
    """Kill conflicting processes"""
    print("\n🔄 Stopping conflicting processes...")
    
    processes = ['rtl_fm', 'rtl_sdr', 'fm_radio', 'simple_fm_radio']
    
    for process in processes:
        try:
            result = subprocess.run(['pkill', '-f', process], capture_output=True)
            if result.returncode == 0:
                print(f"  ✅ Stopped {process}")
            time.sleep(0.5)
        except:
            pass
    
    # Stop systemd services
    services = ['fm-radio.service', 'fm_radio.service']
    for service in services:
        try:
            subprocess.run(['sudo', 'systemctl', 'stop', service], 
                          capture_output=True, timeout=5)
            print(f"  ✅ Stopped {service}")
        except:
            pass

def main():
    print("🎵 FM Radio Diagnostic Tool")
    print("=" * 30)
    
    # Run all checks
    usb_ok = check_usb_devices()
    running_procs = check_running_processes()
    check_systemd_services()
    check_audio_system()
    
    if running_procs:
        print(f"\n⚠️  Found {len(running_procs)} conflicting processes")
        answer = input("Kill conflicting processes? (y/n): ")
        if answer.lower() == 'y':
            kill_conflicting_processes()
            time.sleep(2)
            print("\nRe-checking processes...")
            check_running_processes()
    
    if usb_ok:
        rtl_ok = test_rtl_device()
        if rtl_ok:
            print("\n✅ RTL-SDR device is ready!")
            print("You can now run: python3 simple_fm_radio.py")
        else:
            print("\n❌ RTL-SDR device has issues")
    else:
        print("\n❌ RTL-SDR device not found")
    
    print("\nDiagnostic complete.")

if __name__ == "__main__":
    main()
