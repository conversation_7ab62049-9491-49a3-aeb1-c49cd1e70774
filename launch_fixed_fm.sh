#!/bin/bash
# Launch Fixed FM Radio - Robust Version
# This script handles all the cleanup and setup automatically

echo "🎵 Starting FIXED FM Radio"
echo "=========================="

# Function to cleanup
cleanup() {
    echo "🧹 Cleaning up..."
    sudo pkill -9 -f rtl_fm 2>/dev/null || true
    sudo pkill -9 -f rtl_sdr 2>/dev/null || true
    sudo pkill -9 -f aplay 2>/dev/null || true
    sudo pkill -9 -f simple_fm_radio 2>/dev/null || true
    sudo pkill -9 -f fm_radio 2>/dev/null || true
    sudo systemctl stop fm-radio.service 2>/dev/null || true
    sleep 2
}

# Cleanup first
cleanup

# Check if RTL-SDR device exists
echo "🔍 Checking RTL-SDR device..."
if ! lsusb | grep -i rtl > /dev/null; then
    echo "❌ RTL-SDR device not found!"
    echo "Please connect your RTL-SDR dongle and try again."
    read -p "Press Enter to exit..."
    exit 1
fi

echo "✅ RTL-SDR device found"

# Set display for GUI
export DISPLAY=:0

# Make sure we're in the right directory
cd /home/<USER>

# Check if the fixed file exists
if [ ! -f "simple_fm_radio_fixed.py" ]; then
    echo "❌ simple_fm_radio_fixed.py not found!"
    echo "Using regular simple_fm_radio.py instead..."
    SCRIPT="simple_fm_radio.py"
else
    echo "✅ Using fixed version"
    SCRIPT="simple_fm_radio_fixed.py"
fi

# Launch the radio
echo "🚀 Launching FM Radio..."
echo "📻 Available frequencies: 88.1, 92.5, 95.3, 95.9, 97.1, 103.7, 106.1 MHz"
echo "🎮 Controls: PLAY/STOP, PREV/NEXT, RECORD, EXIT"
echo ""

# Run with error handling
python3 "$SCRIPT"

# Cleanup on exit
cleanup

echo "👋 FM Radio closed"
