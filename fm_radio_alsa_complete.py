#!/usr/bin/env python3
"""
FM Radio DVR with ALSA - Complete Setup
=======================================
- Uses ALSA for reliable audio on Raspberry Pi
- Full DVR functionality with buffering
- Automatic audio device detection
- Touch-friendly interface
"""

import os
import sys
import pygame
import subprocess
import signal
import time
import threading
from pathlib import Path
from enum import Enum
import json

# FM frequencies
FREQUENCIES = [88.1, 92.5, 95.3, 95.9, 97.1, 103.7, 106.1]

# DVR Configuration
BUFFER_SIZE_MB = 100  # 100MB buffer
CHUNK_DURATION_SECONDS = 30  # 30 second chunks

class PlaybackState(Enum):
    LIVE = "live"
    PAUSED = "paused"
    BUFFERED = "buffered"

def setup_alsa():
    """Setup ALSA audio system"""
    print("🔧 Setting up ALSA audio system...")
    
    # Find available audio devices
    try:
        result = subprocess.run(['aplay', '-l'], capture_output=True, text=True)
        print("📱 Available audio devices:")
        for line in result.stdout.split('\n'):
            if 'card' in line:
                print(f"   {line.strip()}")
        
        # Try to find HifiBerry or default device
        audio_device = "default"
        if "hifiberry" in result.stdout.lower() or "dac" in result.stdout.lower():
            audio_device = "hw:0,0"  # Usually HifiBerry
            print("✅ Found HifiBerry DAC")
        else:
            print("✅ Using default audio device")
            
        return audio_device
        
    except Exception as e:
        print(f"⚠️ ALSA setup warning: {e}")
        return "default"

def get_buffer_dir():
    """Get the best buffer directory"""
    possible_dirs = [
        "/tmp/fm_radio_buffer",
        "/home/<USER>/fm_radio_buffer",
        "./fm_radio_buffer"
    ]
    
    for dir_path in possible_dirs:
        try:
            path = Path(dir_path)
            path.mkdir(parents=True, exist_ok=True)
            # Test write access
            test_file = path / "test_write"
            test_file.write_text("test")
            test_file.unlink()
            print(f"✅ Using buffer directory: {dir_path}")
            return str(path)
        except (OSError, PermissionError):
            continue
    
    # Fallback
    fallback = Path("./buffer")
    fallback.mkdir(exist_ok=True)
    return str(fallback)

class AudioBuffer:
    """Manages circular audio buffer for DVR"""
    
    def __init__(self, buffer_dir=None, max_size_mb=BUFFER_SIZE_MB):
        self.buffer_dir = Path(buffer_dir or get_buffer_dir())
        self.max_size_mb = max_size_mb
        self.chunks = []
        self.setup_buffer_dir()
        
    def setup_buffer_dir(self):
        """Create buffer directory and clean up old files"""
        self.buffer_dir.mkdir(exist_ok=True)
        for file in self.buffer_dir.glob("chunk_*.wav"):
            file.unlink()
            
    def get_chunk_path(self, index):
        """Get path for a specific chunk"""
        return self.buffer_dir / f"chunk_{index:04d}.wav"
        
    def add_chunk(self, chunk_path):
        """Add a new chunk to the circular buffer"""
        if chunk_path.exists():
            self.chunks.append({
                'path': chunk_path,
                'timestamp': time.time(),
                'index': len(self.chunks)
            })
            self.cleanup_old_chunks()
            
    def cleanup_old_chunks(self):
        """Remove old chunks to stay within size limit"""
        # Estimate: 30s chunk at 48kHz 16-bit mono ≈ 2.8MB
        max_chunks = (self.max_size_mb * 1024 * 1024) // (30 * 48000 * 2)
        
        while len(self.chunks) > max_chunks:
            old_chunk = self.chunks.pop(0)
            if old_chunk['path'].exists():
                old_chunk['path'].unlink()
                
    def get_total_duration(self):
        """Get total buffered duration in seconds"""
        return len(self.chunks) * CHUNK_DURATION_SECONDS
        
    def cleanup_all(self):
        """Clean up all buffer files"""
        for chunk in self.chunks:
            if chunk['path'].exists():
                chunk['path'].unlink()
        self.chunks.clear()

class FMRadioALSA:
    """FM Radio with ALSA audio and DVR functionality"""
    
    def __init__(self):
        self.current_freq_index = 0
        self.is_playing = False
        self.playback_state = PlaybackState.LIVE
        self.audio_device = setup_alsa()
        self.audio_buffer = AudioBuffer()
        self.playback_position = 0
        
        # Process handles
        self.rtl_proc = None
        self.play_proc = None
        self.record_thread = None
        self.stop_recording = threading.Event()
        
        print(f"✅ FM Radio initialized with audio device: {self.audio_device}")
        
    def start_radio(self):
        """Start FM radio with DVR recording"""
        print("🎵 Starting FM radio with DVR...")
        
        self.stop_radio()
        frequency = FREQUENCIES[self.current_freq_index]
        
        # Start recording for DVR
        self.start_recording(frequency)
        
        # Start live playback
        self.start_live_playback(frequency)
        
        return True
        
    def start_recording(self, frequency):
        """Start recording from RTL-SDR for DVR"""
        self.stop_recording.clear()
        self.record_thread = threading.Thread(
            target=self._record_worker, 
            args=(frequency,), 
            daemon=True
        )
        self.record_thread.start()
        print(f"🔴 Started DVR recording on {frequency} MHz")
        
    def _record_worker(self, frequency):
        """Worker thread for recording audio chunks"""
        chunk_index = 0
        
        while not self.stop_recording.is_set():
            chunk_path = self.audio_buffer.get_chunk_path(chunk_index)
            
            # Record using rtl_fm and ALSA
            cmd = (f"timeout {CHUNK_DURATION_SECONDS} rtl_fm -f {frequency}M -s 1200000 -r 48000 -g 49.6 -l 0 -A fast -E deemp | "
                   f"sox -t raw -r 48000 -e signed -b 16 -c 1 -V1 - -t wav {chunk_path}")
            
            try:
                subprocess.run(cmd, shell=True, check=True, 
                             stdout=subprocess.DEVNULL, 
                             stderr=subprocess.DEVNULL)
                self.audio_buffer.add_chunk(chunk_path)
                chunk_index += 1
                print(f"📼 Recorded chunk {chunk_index} ({self.audio_buffer.get_total_duration()//60}min buffered)")
            except subprocess.CalledProcessError:
                if not self.stop_recording.is_set():
                    print(f"Recording error for chunk {chunk_index}")
                break
                
    def start_live_playback(self, frequency):
        """Start live playback using ALSA"""
        self.cleanup_playback()
        self.playback_state = PlaybackState.LIVE
        
        # Use ALSA for playback
        cmd = (f"rtl_fm -f {frequency}M -s 1200000 -r 48000 -g 49.6 -l 0 -A fast -E deemp | "
               f"aplay -D {self.audio_device} -r 48000 -f S16_LE")
        
        self.play_proc = subprocess.Popen(cmd, shell=True, preexec_fn=os.setsid)
        self.is_playing = True
        print(f"🎵 Live playback started on {frequency} MHz using {self.audio_device}")
        
    def pause(self):
        """Pause playback and switch to buffered mode"""
        if self.playback_state == PlaybackState.LIVE:
            self.cleanup_playback()
            self.playback_state = PlaybackState.PAUSED
            self.playback_position = self.audio_buffer.get_total_duration()
            print("⏸️ Paused - switched to buffered mode")
            
    def resume_live(self):
        """Resume live playback"""
        frequency = FREQUENCIES[self.current_freq_index]
        self.start_live_playback(frequency)
        print("▶️ Resumed live playback")
        
    def play_buffered(self, position_seconds=None):
        """Play from buffered content using ALSA"""
        if not self.audio_buffer.chunks:
            print("❌ No buffered content available")
            return False
            
        self.cleanup_playback()
        self.playback_state = PlaybackState.BUFFERED
        
        if position_seconds is not None:
            self.playback_position = max(0, position_seconds)
            
        # Find the chunk containing this position
        chunk_index = int(self.playback_position // CHUNK_DURATION_SECONDS)
        chunk_index = min(chunk_index, len(self.audio_buffer.chunks) - 1)
        
        if chunk_index < len(self.audio_buffer.chunks):
            chunk_path = self.audio_buffer.chunks[chunk_index]['path']
            if chunk_path.exists():
                self.play_proc = subprocess.Popen(
                    f"aplay -D {self.audio_device} {chunk_path}", 
                    shell=True, 
                    preexec_fn=os.setsid
                )
                print(f"▶️ Playing buffered content from {self.playback_position}s")
                return True
        return False
        
    def rewind(self, seconds=30):
        """Rewind by specified seconds"""
        if self.playback_state != PlaybackState.LIVE:
            self.playback_position = max(0, self.playback_position - seconds)
            self.play_buffered(self.playback_position)
            print(f"⏪ Rewound {seconds} seconds to {self.playback_position}s")
            
    def fast_forward(self, seconds=30):
        """Fast forward by specified seconds"""
        if self.playback_state != PlaybackState.LIVE:
            max_position = self.audio_buffer.get_total_duration()
            self.playback_position = min(max_position, self.playback_position + seconds)
            
            # If we've reached the end, go back to live
            if self.playback_position >= max_position - CHUNK_DURATION_SECONDS:
                self.resume_live()
            else:
                self.play_buffered(self.playback_position)
            print(f"⏩ Fast forwarded {seconds} seconds to {self.playback_position}s")
                
    def cleanup_playback(self):
        """Clean up playback processes"""
        if self.play_proc and self.play_proc.poll() is None:
            try:
                os.killpg(os.getpgid(self.play_proc.pid), signal.SIGTERM)
                self.play_proc.wait(timeout=2)
            except (subprocess.TimeoutExpired, OSError):
                try:
                    os.killpg(os.getpgid(self.play_proc.pid), signal.SIGKILL)
                except OSError:
                    pass
        self.play_proc = None

    def stop_radio(self):
        """Stop radio playback and recording"""
        print("⏹️ Stopping radio...")
        
        # Stop recording
        self.stop_recording.set()
        
        # Stop playback
        self.cleanup_playback()
            
        self.is_playing = False
        self.playback_state = PlaybackState.LIVE
        print("✅ Radio stopped")
        
    def next_station(self):
        """Switch to next station"""
        self.current_freq_index = (self.current_freq_index + 1) % len(FREQUENCIES)
        if self.is_playing:
            self.start_radio()
        print(f"📻 Station: {FREQUENCIES[self.current_freq_index]} MHz")
        
    def prev_station(self):
        """Switch to previous station"""
        self.current_freq_index = (self.current_freq_index - 1) % len(FREQUENCIES)
        if self.is_playing:
            self.start_radio()
        print(f"📻 Station: {FREQUENCIES[self.current_freq_index]} MHz")
        
    def cleanup_all(self):
        """Clean up all processes and resources"""
        print("🧹 Cleaning up all DVR resources...")
        self.stop_recording.set()
        self.cleanup_playback()
        self.audio_buffer.cleanup_all()
        self.is_playing = False
        print("✅ All resources cleaned up")
