#!/usr/bin/env python3
"""
Buffer Monitor for FM Radio
Monitors disk buffer usage and system resources
"""

import os
import time
import psutil
from pathlib import Path

def get_buffer_dirs():
    """Get all possible buffer directories"""
    return [
        "/tmp/fm_radio_buffer",
        "/media/pi/fm_radio_buffer", 
        "/mnt/sdcard/fm_radio_buffer",
        "/home/<USER>/fm_radio_buffer",
        "./fm_radio_buffer"
    ]

def format_bytes(bytes_val):
    """Format bytes to human readable format"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if bytes_val < 1024.0:
            return f"{bytes_val:.1f} {unit}"
        bytes_val /= 1024.0
    return f"{bytes_val:.1f} TB"

def get_directory_size(path):
    """Get total size of directory"""
    total = 0
    try:
        for entry in Path(path).rglob('*'):
            if entry.is_file():
                total += entry.stat().st_size
    except (OSError, PermissionError):
        pass
    return total

def monitor_system():
    """Monitor system resources and buffer usage"""
    print("FM Radio Buffer Monitor")
    print("=" * 50)
    
    while True:
        try:
            # System resources
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            print(f"\n{time.strftime('%Y-%m-%d %H:%M:%S')}")
            print("-" * 30)
            
            # Memory usage
            print(f"RAM Usage: {memory.percent:.1f}% ({format_bytes(memory.used)}/{format_bytes(memory.total)})")
            print(f"RAM Available: {format_bytes(memory.available)}")
            
            # Disk usage
            print(f"Disk Usage: {disk.percent:.1f}% ({format_bytes(disk.used)}/{format_bytes(disk.total)})")
            print(f"Disk Free: {format_bytes(disk.free)}")
            
            # Buffer directories
            print("\nBuffer Directories:")
            total_buffer_size = 0
            for buffer_dir in get_buffer_dirs():
                if Path(buffer_dir).exists():
                    size = get_directory_size(buffer_dir)
                    total_buffer_size += size
                    file_count = len(list(Path(buffer_dir).glob('*')))
                    print(f"  {buffer_dir}: {format_bytes(size)} ({file_count} files)")
            
            print(f"Total Buffer Size: {format_bytes(total_buffer_size)}")
            
            # Process information
            fm_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'cpu_percent']):
                try:
                    if any(keyword in proc.info['name'].lower() for keyword in ['rtl_fm', 'aplay', 'sox', 'python']):
                        if 'fm_radio' in ' '.join(proc.cmdline()).lower():
                            fm_processes.append(proc)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            if fm_processes:
                print("\nFM Radio Processes:")
                for proc in fm_processes:
                    memory_mb = proc.info['memory_info'].rss / 1024 / 1024
                    print(f"  PID {proc.info['pid']}: {proc.info['name']} - {memory_mb:.1f}MB RAM")
            
            time.sleep(5)  # Update every 5 seconds
            
        except KeyboardInterrupt:
            print("\nMonitoring stopped.")
            break
        except Exception as e:
            print(f"Error: {e}")
            time.sleep(5)

if __name__ == "__main__":
    monitor_system()
