#!/usr/bin/env python3
"""
Simple FM Radio - FIXED VERSION
===============================
- Robust process management
- Automatic conflict resolution
- Better error handling
- Touch-friendly interface
"""

import os
import sys
import pygame
import subprocess
import signal
import time
import threading
from pathlib import Path
import atexit

def cleanup_on_exit():
    """Cleanup function called on exit"""
    print("\n🧹 Cleaning up...")
    kill_all_rtl_processes()

def kill_all_rtl_processes():
    """Safely kill RTL-SDR related processes (but not ourselves)"""
    current_pid = os.getpid()

    processes_to_kill = [
        'rtl_fm', 'rtl_sdr', 'rtl_test', 'rtl_tcp', 'rtl_power'
    ]

    # Kill specific RTL processes only
    for process in processes_to_kill:
        try:
            subprocess.run(['pkill', '-f', process],
                          capture_output=True, timeout=2)
        except:
            pass

    # Kill old FM radio processes, but exclude our current process
    try:
        result = subprocess.run(['pgrep', '-f', 'fm_radio'],
                               capture_output=True, text=True)
        for pid in result.stdout.strip().split('\n'):
            if pid and int(pid) != current_pid:
                try:
                    subprocess.run(['kill', pid], capture_output=True, timeout=1)
                except:
                    pass
    except:
        pass

def check_and_fix_rtl_device():
    """Simple RTL-SDR device check"""
    print("🔧 Checking RTL-SDR device...")

    # Gentle cleanup first
    try:
        subprocess.run(['pkill', 'rtl_fm'], capture_output=True, timeout=2)
        subprocess.run(['pkill', 'aplay'], capture_output=True, timeout=2)
    except:
        pass

    time.sleep(1)

    # Check USB device
    try:
        result = subprocess.run(['lsusb'], capture_output=True, text=True, timeout=5)
        if 'RTL' in result.stdout or 'Realtek' in result.stdout:
            print("✅ RTL-SDR device found in USB")
            return True
        else:
            print("⚠️ RTL-SDR device not clearly visible, but continuing...")
            return True  # Continue anyway
    except:
        print("⚠️ Cannot check USB devices, continuing anyway...")
        return True  # Continue anyway

# Register cleanup function
atexit.register(cleanup_on_exit)

# FM frequencies
FREQUENCIES = [88.1, 92.5, 95.3, 95.9, 97.1, 103.7, 106.1]

class SimpleFMRadio:
    def __init__(self):
        self.current_freq_index = 0
        self.is_playing = False
        self.radio_proc = None
        self.buffer_dir = Path("/tmp/fm_buffer")
        self.buffer_dir.mkdir(exist_ok=True)

    def start_radio(self):
        """Start playing FM radio with robust error handling"""
        print("🎵 Starting FM radio...")

        # Stop any existing radio first
        self.stop_radio()

        # Kill any conflicting processes
        kill_all_rtl_processes()
        time.sleep(1)

        frequency = FREQUENCIES[self.current_freq_index]

        # Try multiple command variations for better compatibility
        commands = [
            # Original working command
            f"rtl_fm -f {frequency}M -s 1200000 -r 48000 -g 49.6 -l 0 -A fast -E deemp | aplay -r 48000 -f S16_LE",
            # Alternative with different parameters
            f"rtl_fm -f {frequency}M -s 200000 -r 48000 -g 30 -l 0 -E deemp | aplay -r 48000 -f S16_LE",
            # Simpler version
            f"rtl_fm -f {frequency}M -M fm -s 200000 -g 40 | aplay -r 200000 -f S16_LE"
        ]

        for i, cmd in enumerate(commands):
            print(f"🎛️ Trying command {i+1}: {cmd}")

            try:
                # Use timeout to prevent hanging
                self.radio_proc = subprocess.Popen(
                    f"timeout 30 {cmd}",
                    shell=True,
                    preexec_fn=os.setsid,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.PIPE
                )

                # Wait a moment to see if it starts successfully
                time.sleep(2)

                if self.radio_proc.poll() is None:  # Still running
                    self.is_playing = True
                    print(f"✅ Successfully playing {frequency} MHz")
                    return True
                else:
                    print(f"❌ Command {i+1} failed")

            except Exception as e:
                print(f"❌ Error with command {i+1}: {e}")

        print("❌ All radio commands failed")
        return False
            
    def stop_radio(self):
        """Stop radio playback with aggressive cleanup"""
        print("⏹️ Stopping radio...")

        if self.radio_proc:
            try:
                # Try graceful termination first
                os.killpg(os.getpgid(self.radio_proc.pid), signal.SIGTERM)
                self.radio_proc.wait(timeout=2)
            except:
                try:
                    # Force kill if needed
                    os.killpg(os.getpgid(self.radio_proc.pid), signal.SIGKILL)
                except:
                    pass
            self.radio_proc = None

        # Kill any remaining RTL processes
        kill_all_rtl_processes()

        self.is_playing = False
        print("✅ Radio stopped and cleaned up")
        
    def next_station(self):
        """Switch to next station"""
        self.current_freq_index = (self.current_freq_index + 1) % len(FREQUENCIES)
        if self.is_playing:
            self.start_radio()
        print(f"📻 Station: {FREQUENCIES[self.current_freq_index]} MHz")
        
    def prev_station(self):
        """Switch to previous station"""
        self.current_freq_index = (self.current_freq_index - 1) % len(FREQUENCIES)
        if self.is_playing:
            self.start_radio()
        print(f"📻 Station: {FREQUENCIES[self.current_freq_index]} MHz")
        
    def record_chunk(self):
        """Record a 30-second chunk (stops radio temporarily)"""
        if self.is_playing:
            self.stop_radio()
            
        frequency = FREQUENCIES[self.current_freq_index]
        timestamp = int(time.time())
        filename = self.buffer_dir / f"chunk_{frequency}MHz_{timestamp}.wav"
        
        cmd = f"timeout 30 rtl_fm -f {frequency}M -s 1200000 -r 48000 -g 49.6 -l 0 -A fast -E deemp | sox -t raw -r 48000 -e signed -b 16 -c 1 -V1 - -t wav {filename}"
        
        def record():
            try:
                print(f"🔴 Recording 30 seconds...")
                subprocess.run(cmd, shell=True, check=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                print(f"💾 Recorded: {filename.name}")
            except:
                print("❌ Recording failed")
                
        threading.Thread(target=record, daemon=True).start()

def create_gui():
    """Create pygame interface"""
    pygame.init()
    screen = pygame.display.set_mode((480, 320))
    pygame.display.set_caption('FM Radio - Simple & Working')
    
    font_large = pygame.font.Font(None, 48)
    font_medium = pygame.font.Font(None, 36)
    font_small = pygame.font.Font(None, 24)
    
    # Colors
    BLACK = (0, 0, 0)
    WHITE = (255, 255, 255)
    GREEN = (0, 200, 0)
    RED = (200, 0, 0)
    BLUE = (0, 100, 200)
    GRAY = (100, 100, 100)
    
    return screen, font_large, font_medium, font_small, (BLACK, WHITE, GREEN, RED, BLUE, GRAY)

def draw_button(screen, rect, text, font, bg_color, text_color, border_color):
    """Draw a button"""
    pygame.draw.rect(screen, bg_color, rect)
    pygame.draw.rect(screen, border_color, rect, 3)
    
    text_surf = font.render(text, True, text_color)
    text_rect = text_surf.get_rect(center=rect.center)
    screen.blit(text_surf, text_rect)

def main():
    print("🎵 Simple FM Radio - FIXED VERSION")
    print("=" * 40)

    # Run diagnostics and fixes
    print("🔧 Checking and fixing RTL-SDR device...")
    if not check_and_fix_rtl_device():
        print("❌ RTL-SDR device issues detected. Trying to continue anyway...")
        print("   If problems persist:")
        print("   1. Unplug and replug RTL-SDR dongle")
        print("   2. Reboot Raspberry Pi")
        print("   3. Check if dongle LED is on")
        time.sleep(3)  # Give user time to read

    screen, font_large, font_medium, font_small, colors = create_gui()
    BLACK, WHITE, GREEN, RED, BLUE, GRAY = colors

    radio = SimpleFMRadio()
    clock = pygame.time.Clock()
    running = True

    # Button definitions
    play_btn = pygame.Rect(50, 80, 120, 60)
    stop_btn = pygame.Rect(190, 80, 120, 60)
    prev_btn = pygame.Rect(50, 160, 80, 50)
    next_btn = pygame.Rect(150, 160, 80, 50)
    record_btn = pygame.Rect(250, 160, 100, 50)
    exit_btn = pygame.Rect(370, 160, 80, 50)

    print("✅ Simple FM Radio Started Successfully")
    print("📻 Click PLAY to start radio")
    print("🎛️ Use PREV/NEXT to change stations")
    
    try:
        while running:
            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        running = False
                elif event.type == pygame.MOUSEBUTTONDOWN:
                    pos = event.pos
                    
                    if play_btn.collidepoint(pos):
                        print("▶️ PLAY clicked")
                        radio.start_radio()
                    elif stop_btn.collidepoint(pos):
                        print("⏹️ STOP clicked")
                        radio.stop_radio()
                    elif prev_btn.collidepoint(pos):
                        print("⏮️ PREV clicked")
                        radio.prev_station()
                    elif next_btn.collidepoint(pos):
                        print("⏭️ NEXT clicked")
                        radio.next_station()
                    elif record_btn.collidepoint(pos):
                        print("🔴 RECORD clicked")
                        radio.record_chunk()
                    elif exit_btn.collidepoint(pos):
                        print("❌ EXIT clicked")
                        running = False
            
            # Draw interface
            screen.fill(BLACK)
            
            # Title
            title = font_large.render("FM Radio", True, WHITE)
            screen.blit(title, (20, 20))
            
            # Current frequency
            freq_text = font_medium.render(f"{FREQUENCIES[radio.current_freq_index]:.1f} MHz", True, WHITE)
            screen.blit(freq_text, (300, 30))
            
            # Status
            status_color = GREEN if radio.is_playing else RED
            status_text = "PLAYING" if radio.is_playing else "STOPPED"
            status = font_small.render(f"Status: {status_text}", True, status_color)
            screen.blit(status, (20, 60))
            
            # Draw buttons
            play_color = GREEN if not radio.is_playing else GRAY
            stop_color = RED if radio.is_playing else GRAY
            
            draw_button(screen, play_btn, "PLAY", font_medium, play_color, WHITE, WHITE)
            draw_button(screen, stop_btn, "STOP", font_medium, stop_color, WHITE, WHITE)
            draw_button(screen, prev_btn, "PREV", font_small, BLUE, WHITE, WHITE)
            draw_button(screen, next_btn, "NEXT", font_small, BLUE, WHITE, WHITE)
            draw_button(screen, record_btn, "RECORD", font_small, RED, WHITE, WHITE)
            draw_button(screen, exit_btn, "EXIT", font_small, GRAY, WHITE, WHITE)
            
            # Instructions
            inst1 = font_small.render("Click PLAY to start radio", True, WHITE)
            inst2 = font_small.render("PREV/NEXT to change stations", True, WHITE)
            inst3 = font_small.render("RECORD for 30-sec chunk", True, WHITE)
            screen.blit(inst1, (20, 230))
            screen.blit(inst2, (20, 250))
            screen.blit(inst3, (20, 270))
            
            pygame.display.flip()
            clock.tick(30)
            
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    finally:
        radio.stop_radio()
        pygame.quit()
        print("👋 Simple FM Radio closed")

def signal_handler(sig, frame):
    """Handle Ctrl+C and other signals"""
    print("\n🛑 Signal received, cleaning up...")
    cleanup_on_exit()
    sys.exit(0)

if __name__ == "__main__":
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
        cleanup_on_exit()
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        cleanup_on_exit()
        raise
