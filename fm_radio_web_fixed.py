"""
FM Radio DVR Touchscreen App for Raspberry Pi (FIXED VERSION)
--------------------------------------------------------------------
FIXES APPLIED:
- RTL-FM parameters corrected (fixes static issue)
- Buffer size reduced from 345MB to 3MB RAM (99% reduction)
- Audio device configured for HifiBerry DAC
- Memory optimizations for Raspberry Pi stability

- Uses rtl_fm for demodulation, python-sounddevice for playback, <PERSON><PERSON> for touchscreen UI
- 30 second circular audio buffer (minimal RAM usage)
- EQ presets (rock, country, rap, talk) and volume control in settings
- Up to 15 station presets, easily added
- Single master squelch setting (adjustable in settings)
- Scan button seeks next strong station, stops & buffers
- RDS via background rtl_fm | redsea every 10 seconds, scrolls text
- 15s skip/rewind, persistent seek slider, "Buffer" button jumps to oldest
"""

import os
import sys
import subprocess
import threading
import time
import queue
import signal
import json
from collections import deque
import numpy as np
from scipy import signal as scipy_signal
from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.gridlayout import GridLayout
from kivy.uix.button import Button
from kivy.uix.slider import Slider
from kivy.uix.label import Label
from kivy.uix.popup import Popup
from kivy.uix.textinput import TextInput
from kivy.uix.screenmanager import ScreenManager, Screen
from kivy.properties import StringProperty, NumericProperty, BooleanProperty
from kivy.clock import Clock
import sounddevice as sd

# ---- CONFIGURABLE CONSTANTS (FIXED FOR LOW RAM USAGE) ----
BUFFER_SECONDS = 30     # FIXED: 30 seconds instead of 3600 (saves 342 MB RAM!)
SR = 48000              # Sample rate Hz
CHANNELS = 1            # mono
SAMPLEWIDTH = 2         # bytes per sample (16-bit)
BUFFER_FILE = '/tmp/fmdvr_buffer.raw'
MAX_PRESETS = 15
RDS_POLL_SEC = 10
CHUNK_SIZE = 1024       # FIXED: Reduced from 4096 to 1024
PRESETS_FILE = '/tmp/fmdvr_presets.json'

# FM band limits for your region (adjust as needed)
FM_MIN = 87.5
FM_MAX = 108.0
FM_STEP = 0.1

# Simplified EQ presets (reduced complexity for stability)
PRESET_EQS = {
    'Normal':  [0, 0, 0],
    'Rock':    [3, 0, 3],
    'Country': [2, 0, 2],
    'Talk':    [-2, 3, 1],
}
EQ_BANDS = [250, 1000, 4000]  # Simplified to 3 bands

def db2amp(db):
    return 10 ** (db / 20)

def clamp(val, lo, hi):
    return max(lo, min(hi, val))

def format_freq(f):
    return f"{f:.1f} MHz"

def find_hifiberry_device():
    """Find HifiBerry DAC device - FIXED AUDIO DEVICE DETECTION"""
    try:
        devices = sd.query_devices()
        for i, device in enumerate(devices):
            name = device['name'].lower()
            if any(keyword in name for keyword in ['hifiberry', 'pcm5102a', 'dac']):
                if device['max_output_channels'] > 0:
                    print(f"Found HifiBerry device: {device['name']} (ID: {i})")
                    return i
        print("HifiBerry not found, using default device")
        return None
    except Exception as e:
        print(f"Error finding audio device: {e}")
        return None

# ---- RADIO + BUFFER CORE (FIXED VERSION) ----
class RadioBackend:
    """
    Handles radio tuning, buffer management, RDS, and playback
    FIXED VERSION with proper RTL-FM parameters and minimal RAM usage
    """
    def __init__(self):
        self.freq = 88.1
        self.gain = 30  # FIXED: Reduced from 40 to 30
        self.squelch = 0
        self.proc = None
        self.running = False
        self.buffer_size = SR * BUFFER_SECONDS  # FIXED: Much smaller buffer
        self.buffer = np.zeros(self.buffer_size, dtype=np.int16)
        self.write_ptr = 0  # Next write
        self.playback_ptr = 0  # Current play
        self.lock = threading.Lock()
        self.is_paused = False
        self.eq_preset = 'Normal'
        self.presets = self.load_presets()
        self.rds_text = ''
        self.rds_thread = None
        self.rds_stop = threading.Event()
        self.volume_gain = 1.0
        self.scanning = False
        self.scan_stop = threading.Event()
        self.scan_threshold_db = -35
        self.audio_stream = None
        self.audio_thread = None
        self.audio_stop = threading.Event()
        self.audio_queue = queue.Queue(maxsize=3)  # FIXED: Reduced from 10 to 3
        self.rds_proc = None
        
        # FIXED: Find HifiBerry device
        self.audio_device = find_hifiberry_device()
        
        # Simplified EQ filter coefficients
        self.eq_filters = {}
        self._init_eq_filters()
        
        # Signal strength monitoring
        self.signal_strength = 0
        self.signal_history = deque(maxlen=50)
        
        print(f"FIXED RadioBackend initialized:")
        print(f"  Buffer size: {BUFFER_SECONDS} seconds ({self.buffer_size * 2 / 1024 / 1024:.1f} MB)")
        print(f"  Gain: {self.gain} (reduced from 40)")
        print(f"  Audio device: {self.audio_device}")

    def _init_eq_filters(self):
        """Initialize simplified butterworth bandpass filters for EQ bands"""
        nyquist = SR / 2
        for i, freq in enumerate(EQ_BANDS):
            if freq < nyquist:
                try:
                    # Simplified filter design
                    if i == 0:
                        # Low pass for bass
                        sos = scipy_signal.butter(2, freq / nyquist, btype='low', output='sos')
                    elif i == len(EQ_BANDS) - 1:
                        # High pass for treble
                        sos = scipy_signal.butter(2, freq / nyquist, btype='high', output='sos')
                    else:
                        # Bandpass for mid
                        low = EQ_BANDS[i-1] / nyquist
                        high = freq / nyquist
                        sos = scipy_signal.butter(2, [low, high], btype='band', output='sos')
                    
                    self.eq_filters[i] = sos
                except Exception as e:
                    print(f"EQ filter {i} failed: {e}")
                    self.eq_filters[i] = None

    def load_presets(self):
        """Load presets from file or return defaults"""
        try:
            with open(PRESETS_FILE, 'r') as f:
                return json.load(f)
        except:
            return [88.1, 92.5, 95.3, 95.9, 97.1, 103.7, 106.1]

    def save_presets(self):
        """Save current presets to file"""
        try:
            with open(PRESETS_FILE, 'w') as f:
                json.dump(self.presets, f)
        except:
            pass

    def start_radio(self):
        """FIXED: Start radio with corrected RTL-FM parameters"""
        self.stop_radio()  # Cleanup previous

        # Check if RTL-SDR is available
        try:
            subprocess.run(['rtl_test', '-t'], capture_output=True, timeout=1)
        except:
            print("Error: RTL-SDR device not found!")
            return False

        # FIXED: Proper RTL-FM command with correct parameters
        rtl_cmd = [
            'rtl_fm',
            '-f', f'{self.freq}M',
            '-M', 'fm',
            '-s', str(SR),          # 48000 sample rate
            '-r', str(SR),          # FIXED: Add resample rate (was missing!)
            '-g', '30',             # FIXED: Use 30 instead of 40 (was too high!)
            '-l', str(self.squelch),
            '-E', 'deemp',          # De-emphasis
            '-E', 'dc'              # FIXED: DC blocking (was '-A', 'fast', '-F', '9')
        ]

        print(f"FIXED RTL-FM command: {' '.join(rtl_cmd)}")

        try:
            self.proc = subprocess.Popen(rtl_cmd, stdout=subprocess.PIPE,
                                       stderr=subprocess.PIPE, bufsize=8192)
            self.running = True
            self.audio_stop.clear()
            self.audio_thread = threading.Thread(target=self._audio_reader, daemon=True)
            self.audio_thread.start()

            # Start RDS if not running
            if not self.rds_thread or not self.rds_thread.is_alive():
                self.rds_stop.clear()
                self.rds_thread = threading.Thread(target=self._rds_worker, daemon=True)
                self.rds_thread.start()

            print(f"✅ Radio started successfully on {self.freq} MHz")
            return True
        except Exception as e:
            print(f"Error starting radio: {e}")
            return False

    def stop_radio(self):
        """Stop radio and cleanup"""
        self.running = False
        self.audio_stop.set()
        self.scan_stop.set()

        if self.proc:
            try:
                self.proc.terminate()
                self.proc.wait(timeout=2)
            except:
                self.proc.kill()
            self.proc = None

        if self.audio_thread and self.audio_thread.is_alive():
            self.audio_thread.join(timeout=2)

        if self.audio_stream:
            self.audio_stream.stop()
            self.audio_stream.close()
            self.audio_stream = None

        # Clear audio queue
        while not self.audio_queue.empty():
            try:
                self.audio_queue.get_nowait()
            except:
                break

    def _audio_reader(self):
        """Read audio from rtl_fm and process it - FIXED VERSION"""
        chunk_size = CHUNK_SIZE * SAMPLEWIDTH  # FIXED: Smaller chunks
        remainder = b''

        while self.running and not self.audio_stop.is_set():
            try:
                # Read with timeout
                data = self.proc.stdout.read(chunk_size - len(remainder))
                if not data:
                    time.sleep(0.01)
                    continue

                # Handle partial samples
                data = remainder + data
                sample_count = len(data) // SAMPLEWIDTH
                usable_bytes = sample_count * SAMPLEWIDTH
                remainder = data[usable_bytes:]

                if sample_count == 0:
                    continue

                samples = np.frombuffer(data[:usable_bytes], dtype=np.int16)

                # Calculate signal strength
                self.signal_strength = np.abs(samples).mean()
                self.signal_history.append(self.signal_strength)

                # Apply squelch
                if self.squelch > 0 and self.signal_strength < self.squelch * 100:
                    samples = np.zeros_like(samples)

                # FIXED: Simplified EQ application
                if self.eq_preset != 'Normal':
                    samples = self.apply_eq_simple(samples)

                # Apply volume
                samples = (samples * self.volume_gain).astype(np.int16)

                # Update circular buffer
                with self.lock:
                    end_ptr = (self.write_ptr + len(samples)) % self.buffer_size

                    if end_ptr > self.write_ptr:
                        self.buffer[self.write_ptr:end_ptr] = samples
                    else:
                        # Wrap around
                        first_part = self.buffer_size - self.write_ptr
                        self.buffer[self.write_ptr:] = samples[:first_part]
                        self.buffer[:end_ptr] = samples[first_part:]

                    self.write_ptr = end_ptr

                # Queue for playback (FIXED: smaller queue)
                try:
                    self.audio_queue.put(samples, block=False)
                except queue.Full:
                    # Drop oldest if queue is full
                    try:
                        self.audio_queue.get_nowait()
                        self.audio_queue.put(samples, block=False)
                    except:
                        pass

            except Exception as e:
                print(f"Audio reader error: {e}")
                time.sleep(0.1)
