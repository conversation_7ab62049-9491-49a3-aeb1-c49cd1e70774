#!/bin/bash
# FM DVR GUI Launcher Script
# Opens GUI interface for FM DVR with SD card storage

echo "🎵 Starting FM DVR GUI..."
echo "=========================="

# Check if RTL-SDR device is available
if ! lsusb | grep -q "RTL"; then
    echo "❌ RTL-SDR device not found!"
    echo "Please connect your RTL-SDR dongle and try again."
    read -p "Press Enter to exit..."
    exit 1
fi

# Stop any existing FM radio service to prevent conflicts
sudo systemctl stop fm-radio.service 2>/dev/null || true

# Set display for GUI (if running over SSH)
export DISPLAY=:0

# Launch FM DVR GUI
cd /home/<USER>
python3 ./fm_dvr_gui.py

echo "FM DVR GUI closed."
