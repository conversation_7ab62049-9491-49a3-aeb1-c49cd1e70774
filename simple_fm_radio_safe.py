#!/usr/bin/env python3
"""
Simple FM Radio - SAFE VERSION
==============================
- Minimal process management
- No aggressive cleanup
- Simple and reliable
"""

import os
import sys
import pygame
import subprocess
import signal
import time
import threading
from pathlib import Path
from enum import Enum
import tempfile
import queue

# FM frequencies
FREQUENCIES = [88.1, 92.5, 95.3, 95.9, 97.1, 103.7, 106.1]

# DVR Configuration
BUFFER_SIZE_MB = 50  # Reduced for Pi stability
CHUNK_DURATION_SECONDS = 30  # 30 second chunks

# Buffer directories (in order of preference)
POSSIBLE_BUFFER_DIRS = [
    "/tmp/fm_radio_buffer",
    "/home/<USER>/fm_radio_buffer",
    "/home/<USER>/fm_radio_buffer"
]

class PlaybackState(Enum):
    LIVE = "live"
    PAUSED = "paused"
    BUFFERED = "buffered"

def get_best_buffer_dir():
    """Find the best available directory for buffering"""
    for dir_path in POSSIBLE_BUFFER_DIRS:
        try:
            path = Path(dir_path)
            path.mkdir(parents=True, exist_ok=True)
            test_file = path / "test_write"
            test_file.write_text("test")
            test_file.unlink()
            return str(path)
        except (OSError, PermissionError):
            continue

    fallback = Path("./fm_radio_buffer")
    fallback.mkdir(exist_ok=True)
    return str(fallback)

class AudioBuffer:
    """Manages circular audio buffer for DVR functionality"""

    def __init__(self, buffer_dir=None, max_size_mb=BUFFER_SIZE_MB):
        if buffer_dir is None:
            buffer_dir = get_best_buffer_dir()

        self.buffer_dir = Path(buffer_dir)
        self.max_size_mb = max_size_mb
        self.chunk_duration = CHUNK_DURATION_SECONDS
        self.chunks = []
        self.current_chunk_index = 0
        self.setup_buffer_dir()

    def setup_buffer_dir(self):
        """Create buffer directory and clean up old files"""
        self.buffer_dir.mkdir(exist_ok=True)
        for file in self.buffer_dir.glob("chunk_*.wav"):
            file.unlink()

    def get_chunk_path(self, index):
        """Get path for a specific chunk"""
        return self.buffer_dir / f"chunk_{index:04d}.wav"

    def add_chunk(self, chunk_path):
        """Add a new chunk to the circular buffer"""
        if chunk_path.exists():
            self.chunks.append({
                'path': chunk_path,
                'timestamp': time.time(),
                'index': len(self.chunks)
            })

            # Remove old chunks if we exceed buffer size
            self.cleanup_old_chunks()

    def cleanup_old_chunks(self):
        """Remove old chunks to stay within size limit"""
        max_chunks = (self.max_size_mb * 1024 * 1024) // (self.chunk_duration * 48000 * 2 * 2)  # Rough estimate

        while len(self.chunks) > max_chunks:
            old_chunk = self.chunks.pop(0)
            if old_chunk['path'].exists():
                old_chunk['path'].unlink()

    def get_available_chunks(self):
        """Get list of available buffered chunks"""
        return [chunk for chunk in self.chunks if chunk['path'].exists()]

    def cleanup_all(self):
        """Clean up all buffer files"""
        for chunk in self.chunks:
            if chunk['path'].exists():
                chunk['path'].unlink()
        self.chunks.clear()

class SimpleFMRadio:
    def __init__(self):
        self.current_freq_index = 0
        self.is_playing = False
        self.radio_proc = None
        self.playback_state = PlaybackState.LIVE
        self.audio_buffer = AudioBuffer()
        self.current_chunk_index = 0
        self.playback_position = 0  # Position in buffered content (seconds from start)

        # Process handles
        self.rtl_proc = None
        self.play_proc = None
        self.record_thread = None
        self.stop_recording = threading.Event()
        
    def start_radio(self):
        """Start playing FM radio with DVR recording"""
        print("🎵 Starting FM radio with DVR...")

        # Stop any existing radio first
        self.stop_radio()

        frequency = FREQUENCIES[self.current_freq_index]

        # Start recording for DVR
        self.start_recording(frequency)

        # Start live playback
        self.start_live_playback(frequency)

        return True

    def start_recording(self, frequency):
        """Start recording from RTL-SDR for DVR"""
        self.stop_recording.clear()
        self.record_thread = threading.Thread(
            target=self._record_worker,
            args=(frequency,),
            daemon=True
        )
        self.record_thread.start()
        print(f"🔴 Started DVR recording on {frequency} MHz")

    def _record_worker(self, frequency):
        """Worker thread for recording audio chunks"""
        chunk_index = 0

        while not self.stop_recording.is_set():
            chunk_path = self.audio_buffer.get_chunk_path(chunk_index)

            # Record a chunk using rtl_fm and sox
            cmd = (f"timeout {CHUNK_DURATION_SECONDS} rtl_fm -f {frequency}M -s 1200000 -r 48000 -g 49.6 -l 0 -A fast -E deemp | "
                   f"sox -t raw -r 48000 -e signed -b 16 -c 1 -V1 - -t wav {chunk_path}")

            try:
                subprocess.run(cmd, shell=True, check=True,
                             stdout=subprocess.DEVNULL,
                             stderr=subprocess.DEVNULL)
                self.audio_buffer.add_chunk(chunk_path)
                chunk_index += 1
                print(f"📼 Recorded chunk {chunk_index}")
            except subprocess.CalledProcessError:
                if not self.stop_recording.is_set():
                    print(f"Recording error for chunk {chunk_index}")
                break

    def start_live_playback(self, frequency):
        """Start live playback"""
        self.cleanup_playback()
        self.playback_state = PlaybackState.LIVE

        # Start live streaming
        cmd = f"rtl_fm -f {frequency}M -s 1200000 -r 48000 -g 49.6 -l 0 -A fast -E deemp | aplay -r 48000 -f S16_LE"

        self.play_proc = subprocess.Popen(cmd, shell=True, preexec_fn=os.setsid)
        self.is_playing = True
        print(f"🎵 Live playback started on {frequency} MHz")
            
    def pause(self):
        """Pause playback and switch to buffered mode"""
        if self.playback_state == PlaybackState.LIVE:
            self.cleanup_playback()
            self.playback_state = PlaybackState.PAUSED
            self.playback_position = len(self.audio_buffer.chunks) * CHUNK_DURATION_SECONDS
            print("⏸️ Paused - switched to buffered mode")

    def resume_live(self):
        """Resume live playback"""
        frequency = FREQUENCIES[self.current_freq_index]
        self.start_live_playback(frequency)
        print("▶️ Resumed live playback")

    def play_buffered(self, position_seconds=None):
        """Play from buffered content"""
        if not self.audio_buffer.chunks:
            print("❌ No buffered content available")
            return False

        self.cleanup_playback()
        self.playback_state = PlaybackState.BUFFERED

        if position_seconds is not None:
            self.playback_position = max(0, position_seconds)

        # Find the chunk containing this position
        chunk_index = int(self.playback_position // CHUNK_DURATION_SECONDS)
        chunk_index = min(chunk_index, len(self.audio_buffer.chunks) - 1)

        if chunk_index < len(self.audio_buffer.chunks):
            chunk_path = self.audio_buffer.chunks[chunk_index]['path']
            if chunk_path.exists():
                self.play_proc = subprocess.Popen(
                    f"aplay {chunk_path}",
                    shell=True,
                    preexec_fn=os.setsid
                )
                print(f"▶️ Playing buffered content from {self.playback_position}s")
                return True
        return False

    def rewind(self, seconds=30):
        """Rewind by specified seconds"""
        if self.playback_state != PlaybackState.LIVE:
            self.playback_position = max(0, self.playback_position - seconds)
            self.play_buffered(self.playback_position)
            print(f"⏪ Rewound {seconds} seconds")

    def fast_forward(self, seconds=30):
        """Fast forward by specified seconds"""
        if self.playback_state != PlaybackState.LIVE:
            max_position = len(self.audio_buffer.chunks) * CHUNK_DURATION_SECONDS
            self.playback_position = min(max_position, self.playback_position + seconds)

            # If we've reached the end, go back to live
            if self.playback_position >= max_position - CHUNK_DURATION_SECONDS:
                self.resume_live()
            else:
                self.play_buffered(self.playback_position)
            print(f"⏩ Fast forwarded {seconds} seconds")

    def cleanup_playback(self):
        """Clean up playback processes"""
        if self.play_proc and self.play_proc.poll() is None:
            try:
                os.killpg(os.getpgid(self.play_proc.pid), signal.SIGTERM)
                self.play_proc.wait(timeout=2)
            except (subprocess.TimeoutExpired, OSError):
                try:
                    os.killpg(os.getpgid(self.play_proc.pid), signal.SIGKILL)
                except OSError:
                    pass
        self.play_proc = None

    def stop_radio(self):
        """Stop radio playback and recording"""
        print("⏹️ Stopping radio...")

        # Stop recording
        self.stop_recording.set()

        # Stop playback
        self.cleanup_playback()

        # Stop old radio_proc if it exists
        if self.radio_proc:
            try:
                os.killpg(os.getpgid(self.radio_proc.pid), signal.SIGTERM)
                self.radio_proc.wait(timeout=2)
            except:
                try:
                    os.killpg(os.getpgid(self.radio_proc.pid), signal.SIGKILL)
                except:
                    pass
            self.radio_proc = None

        self.is_playing = False
        self.playback_state = PlaybackState.LIVE
        print("✅ Radio stopped")

    def cleanup_all(self):
        """Clean up all processes and resources"""
        print("🧹 Cleaning up all DVR resources...")
        self.stop_recording.set()
        self.cleanup_playback()
        self.audio_buffer.cleanup_all()

        if self.radio_proc:
            try:
                os.killpg(os.getpgid(self.radio_proc.pid), signal.SIGTERM)
                self.radio_proc.wait(timeout=2)
            except:
                try:
                    os.killpg(os.getpgid(self.radio_proc.pid), signal.SIGKILL)
                except:
                    pass
            self.radio_proc = None

        self.is_playing = False
        print("✅ All resources cleaned up")
        
    def next_station(self):
        """Switch to next station"""
        self.current_freq_index = (self.current_freq_index + 1) % len(FREQUENCIES)
        if self.is_playing:
            self.start_radio()
        print(f"📻 Station: {FREQUENCIES[self.current_freq_index]} MHz")
        
    def prev_station(self):
        """Switch to previous station"""
        self.current_freq_index = (self.current_freq_index - 1) % len(FREQUENCIES)
        if self.is_playing:
            self.start_radio()
        print(f"📻 Station: {FREQUENCIES[self.current_freq_index]} MHz")

def create_gui():
    """Create pygame interface with debugging"""
    print("🔧 Setting up SDL environment...")

    # Set display environment for touchscreen
    os.environ['SDL_FBDEV'] = '/dev/fb0'
    os.environ['SDL_MOUSEDEV'] = '/dev/input/touchscreen'
    os.environ['SDL_MOUSEDRV'] = 'TSLIB'
    print("✅ SDL environment variables set")

    print("🔧 Initializing pygame...")
    pygame.init()
    print("✅ Pygame initialized")

    print("🔧 Creating display (800x480)...")
    try:
        screen = pygame.display.set_mode((800, 480))
        print("✅ Display created successfully!")
    except Exception as e:
        print(f"❌ Display creation failed: {e}")
        print("🔧 Trying smaller display (480x320)...")
        screen = pygame.display.set_mode((480, 320))
        print("✅ Smaller display created!")

    pygame.display.set_caption('FM DVR Radio - Debug')
    print("✅ Window caption set")

    # Hide mouse cursor for touchscreen
    pygame.mouse.set_visible(False)
    print("✅ Mouse cursor hidden")
    
    font_large = pygame.font.Font(None, 48)
    font_medium = pygame.font.Font(None, 36)
    font_small = pygame.font.Font(None, 24)
    
    # Colors
    BLACK = (0, 0, 0)
    WHITE = (255, 255, 255)
    GREEN = (0, 200, 0)
    RED = (200, 0, 0)
    BLUE = (0, 100, 200)
    GRAY = (100, 100, 100)
    
    return screen, font_large, font_medium, font_small, (BLACK, WHITE, GREEN, RED, BLUE, GRAY)

def draw_button(screen, rect, text, font, bg_color, text_color, border_color):
    """Draw a button"""
    pygame.draw.rect(screen, bg_color, rect)
    pygame.draw.rect(screen, border_color, rect, 3)
    
    text_surf = font.render(text, True, text_color)
    text_rect = text_surf.get_rect(center=rect.center)
    screen.blit(text_surf, text_rect)

def main():
    print("🎵 FM DVR Radio - DEBUG VERSION")
    print("=" * 40)

    # Debug environment info
    print(f"🔍 Current user: {os.getenv('USER', 'unknown')}")
    print(f"🔍 Display: {os.getenv('DISPLAY', 'not set')}")
    print(f"🔍 Desktop session: {os.getenv('DESKTOP_SESSION', 'not set')}")
    print(f"🔍 XDG desktop: {os.getenv('XDG_CURRENT_DESKTOP', 'not set')}")

    # Set display for touchscreen
    os.environ['DISPLAY'] = ':0'
    print("✅ Set DISPLAY=:0")

    # Quick cleanup
    try:
        subprocess.run(['pkill', 'rtl_fm'], capture_output=True, timeout=1)
        print("✅ Cleaned up old processes")
    except:
        pass

    print("🖥️ Initializing pygame...")
    
    screen, font_large, font_medium, font_small, colors = create_gui()
    BLACK, WHITE, GREEN, RED, BLUE, GRAY = colors
    
    radio = SimpleFMRadio()
    clock = pygame.time.Clock()
    running = True
    
    # Button definitions - Larger DVR Layout
    play_btn = pygame.Rect(50, 100, 120, 60)
    stop_btn = pygame.Rect(180, 100, 120, 60)
    pause_btn = pygame.Rect(310, 100, 120, 60)
    live_btn = pygame.Rect(440, 100, 120, 60)

    prev_btn = pygame.Rect(50, 180, 100, 50)
    next_btn = pygame.Rect(160, 180, 100, 50)
    rewind_btn = pygame.Rect(270, 180, 120, 50)
    ff_btn = pygame.Rect(400, 180, 120, 50)
    exit_btn = pygame.Rect(530, 180, 100, 50)
    
    print("✅ Simple FM Radio Started")
    print("📻 Click PLAY to start radio")
    print("🎛️ Use PREV/NEXT to change stations")
    
    try:
        while running:
            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        running = False
                elif event.type == pygame.MOUSEBUTTONDOWN:
                    pos = event.pos

                    if play_btn.collidepoint(pos):
                        print("▶️ PLAY clicked")
                        radio.start_radio()
                    elif stop_btn.collidepoint(pos):
                        print("⏹️ STOP clicked")
                        radio.stop_radio()
                    elif pause_btn.collidepoint(pos):
                        print("⏸️ PAUSE clicked")
                        radio.pause()
                    elif live_btn.collidepoint(pos):
                        print("📡 LIVE clicked")
                        radio.resume_live()
                    elif prev_btn.collidepoint(pos):
                        print("⏮️ PREV clicked")
                        radio.prev_station()
                    elif next_btn.collidepoint(pos):
                        print("⏭️ NEXT clicked")
                        radio.next_station()
                    elif rewind_btn.collidepoint(pos):
                        print("⏪ REWIND clicked")
                        radio.rewind(30)
                    elif ff_btn.collidepoint(pos):
                        print("⏩ FAST FORWARD clicked")
                        radio.fast_forward(30)
                    elif exit_btn.collidepoint(pos):
                        print("❌ EXIT clicked")
                        running = False
            
            # Draw interface
            screen.fill(BLACK)

            # Title
            title = font_large.render("FM DVR RADIO", True, WHITE)
            screen.blit(title, (50, 20))

            # Current frequency - larger
            freq_text = font_large.render(f"{FREQUENCIES[radio.current_freq_index]:.1f} MHz", True, WHITE)
            screen.blit(freq_text, (450, 20))

            # Status and DVR state - more prominent
            status_color = GREEN if radio.is_playing else RED
            status_text = "🎵 PLAYING" if radio.is_playing else "⏹️ STOPPED"
            status = font_medium.render(f"{status_text}", True, status_color)
            screen.blit(status, (50, 70))

            # DVR state - larger and more obvious
            state_color = GREEN if radio.playback_state == PlaybackState.LIVE else (RED if radio.playback_state == PlaybackState.PAUSED else BLUE)
            if radio.playback_state == PlaybackState.LIVE:
                state_text = "📡 LIVE"
            elif radio.playback_state == PlaybackState.PAUSED:
                state_text = "⏸️ PAUSED"
            else:
                state_text = "📼 BUFFERED"
            state = font_medium.render(state_text, True, state_color)
            screen.blit(state, (300, 70))

            # Buffer info - more prominent
            buffer_count = len(radio.audio_buffer.chunks)
            buffer_minutes = (buffer_count * CHUNK_DURATION_SECONDS) // 60
            buffer_text = font_medium.render(f"📼 {buffer_count} chunks ({buffer_minutes}min)", True, WHITE)
            screen.blit(buffer_text, (500, 70))

            # Draw buttons with appropriate colors
            play_color = GREEN if not radio.is_playing else GRAY
            stop_color = RED if radio.is_playing else GRAY
            pause_color = RED if radio.playback_state == PlaybackState.LIVE else GRAY
            live_color = GREEN if radio.playback_state == PlaybackState.LIVE else BLUE
            dvr_color = BLUE if radio.playback_state != PlaybackState.LIVE else GRAY

            draw_button(screen, play_btn, "▶️ PLAY", font_medium, play_color, WHITE, WHITE)
            draw_button(screen, stop_btn, "⏹️ STOP", font_medium, stop_color, WHITE, WHITE)
            draw_button(screen, pause_btn, "⏸️ PAUSE", font_medium, pause_color, WHITE, WHITE)
            draw_button(screen, live_btn, "📡 LIVE", font_medium, live_color, WHITE, WHITE)

            draw_button(screen, prev_btn, "⏮️ PREV", font_medium, BLUE, WHITE, WHITE)
            draw_button(screen, next_btn, "⏭️ NEXT", font_medium, BLUE, WHITE, WHITE)
            draw_button(screen, rewind_btn, "⏪ 30s", font_medium, dvr_color, WHITE, WHITE)
            draw_button(screen, ff_btn, "30s ⏩", font_medium, dvr_color, WHITE, WHITE)
            draw_button(screen, exit_btn, "❌ EXIT", font_medium, RED, WHITE, WHITE)

            # Instructions
            inst1 = font_medium.render("DVR: PLAY starts recording | PAUSE to use ⏪⏩", True, WHITE)
            inst2 = font_medium.render("Buffer shows recorded time | LIVE returns to broadcast", True, WHITE)
            screen.blit(inst1, (50, 250))
            screen.blit(inst2, (50, 280))

            pygame.display.flip()
            clock.tick(30)

    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    finally:
        radio.cleanup_all()
        pygame.quit()
        print("👋 FM DVR closed")

if __name__ == "__main__":
    main()

if __name__ == "__main__":
    main()
