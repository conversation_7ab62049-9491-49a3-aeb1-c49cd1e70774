#!/usr/bin/env python3
"""
Simple GUI Test - Minimal pygame window
"""

import os
import pygame
import sys

def main():
    print("🖥️ Simple GUI Test")
    print("=" * 20)
    
    # Set display environment
    os.environ['DISPLAY'] = ':0'
    
    try:
        print("Initializing pygame...")
        pygame.init()
        
        print("Creating display...")
        # Try the exact same size as the working launcher
        screen = pygame.display.set_mode((480, 320))
        pygame.display.set_caption('GUI Test')
        
        print("✅ Display created successfully!")
        print("You should see a window with a red background")
        
        # Simple colors
        RED = (255, 0, 0)
        WHITE = (255, 255, 255)
        
        # Create font
        font = pygame.font.Font(None, 36)
        
        running = True
        while running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        running = False
                elif event.type == pygame.MOUSEBUTTONDOWN:
                    print(f"Mouse clicked at {event.pos}")
                    running = False
            
            # Draw red background
            screen.fill(RED)
            
            # Draw text
            text = font.render("GUI Test - Click to Exit", True, WHITE)
            screen.blit(text, (50, 150))
            
            pygame.display.flip()
        
        pygame.quit()
        print("✅ GUI test completed successfully")
        
    except Exception as e:
        print(f"❌ GUI test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
