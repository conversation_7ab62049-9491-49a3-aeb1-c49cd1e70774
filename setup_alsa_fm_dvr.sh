#!/bin/bash
# Complete ALSA FM DVR Setup Script for Raspberry Pi
# Sets up HifiBerry DAC, equalizer, and FM DVR system

echo "🎵 FM DVR ALSA Setup for Raspberry Pi"
echo "====================================="

# Check if running as root for some operations
if [ "$EUID" -eq 0 ]; then
    echo "⚠️ Running as root - some operations will be done as user josh"
    USER_HOME="/home/<USER>"
else
    echo "✅ Running as user: $USER"
    USER_HOME="$HOME"
fi

echo "🔧 Step 1: Checking current audio setup..."
echo "Available audio devices:"
aplay -l

echo ""
echo "🔧 Step 2: Setting up ALSA configuration..."

# Create ALSA configuration for HifiBerry DAC with equalizer
cat > "$USER_HOME/.asoundrc" << 'EOF'
# ALSA configuration for HifiBerry DAC with equalizer support

# HifiBerry DAC device
pcm.hifiberry {
    type hw
    card 0
    device 0
}

# Equalizer device
pcm.equal {
    type equal
    slave.pcm "hifiberry"
    slave.channels 2
}

# Default device (direct to HifiBerry)
pcm.!default {
    type plug
    slave.pcm "hifiberry"
}

# Control device
ctl.!default {
    type hw
    card 0
}

# Equalizer control
ctl.equal {
    type equal
    slave.pcm "hifiberry"
}
EOF

echo "✅ Created ALSA configuration at $USER_HOME/.asoundrc"

echo ""
echo "🔧 Step 3: Installing required packages..."

# Check and install required packages
PACKAGES="rtl-sdr sox alsa-utils python3-pygame python3-alsaaudio"

for package in $PACKAGES; do
    if dpkg -l | grep -q "^ii  $package "; then
        echo "✅ $package already installed"
    else
        echo "📦 Installing $package..."
        if [ "$EUID" -eq 0 ]; then
            apt-get update && apt-get install -y $package
        else
            sudo apt-get update && sudo apt-get install -y $package
        fi
    fi
done

echo ""
echo "🔧 Step 4: Setting up equalizer..."

# Initialize equalizer if not already done
if [ ! -f "$USER_HOME/.alsaequal.bin" ]; then
    echo "🎛️ Initializing equalizer..."
    # Create a default equalizer state
    alsactl init equal 2>/dev/null || echo "⚠️ Equalizer init warning (this is normal)"
fi

echo ""
echo "🔧 Step 5: Testing audio devices..."

echo "Testing HifiBerry DAC:"
if aplay -D hifiberry /dev/zero -d 1 2>/dev/null; then
    echo "✅ HifiBerry DAC working"
else
    echo "⚠️ HifiBerry DAC test failed"
fi

echo "Testing default device:"
if aplay -D default /dev/zero -d 1 2>/dev/null; then
    echo "✅ Default device working"
else
    echo "⚠️ Default device test failed"
fi

echo ""
echo "🔧 Step 6: Testing RTL-SDR..."

if lsusb | grep -i rtl > /dev/null; then
    echo "✅ RTL-SDR device found"
    if timeout 3 rtl_test -t > /dev/null 2>&1; then
        echo "✅ RTL-SDR device working"
    else
        echo "⚠️ RTL-SDR device test failed (may be in use)"
    fi
else
    echo "❌ RTL-SDR device not found"
fi

echo ""
echo "🔧 Step 7: Creating FM DVR directories..."

# Create necessary directories
mkdir -p "$USER_HOME/fm_dvr_buffer"
mkdir -p "/tmp/fm_dvr"

# Set permissions
if [ "$EUID" -eq 0 ]; then
    chown josh:josh "$USER_HOME/fm_dvr_buffer"
    chown josh:josh "/tmp/fm_dvr"
fi

echo "✅ Created buffer directories"

echo ""
echo "🔧 Step 8: Creating launcher script..."

cat > "$USER_HOME/launch_fm_dvr.sh" << 'EOF'
#!/bin/bash
# FM DVR Launcher Script

echo "🎵 Starting FM DVR..."

# Kill any existing processes
sudo pkill -f rtl_fm 2>/dev/null || true
sudo pkill -f python3.*josh5 2>/dev/null || true
sleep 1

# Set display
export DISPLAY=:0

# Clean up old pipes and buffers
rm -f /tmp/fm_radio_pipe
rm -f /tmp/fm_dvr/segment_*.raw

# Launch FM DVR
cd /home/<USER>
python3 josh5.py

echo "👋 FM DVR closed"
EOF

chmod +x "$USER_HOME/launch_fm_dvr.sh"

if [ "$EUID" -eq 0 ]; then
    chown josh:josh "$USER_HOME/launch_fm_dvr.sh"
fi

echo "✅ Created launcher script at $USER_HOME/launch_fm_dvr.sh"

echo ""
echo "🔧 Step 9: Creating desktop shortcut..."

cat > "$USER_HOME/Desktop/FM_DVR.desktop" << 'EOF'
[Desktop Entry]
Version=1.0
Type=Application
Name=FM DVR Radio
Comment=FM Radio with DVR functionality
Exec=/home/<USER>/launch_fm_dvr.sh
Icon=multimedia-volume-control
Terminal=false
Categories=AudioVideo;Audio;
EOF

chmod +x "$USER_HOME/Desktop/FM_DVR.desktop"

if [ "$EUID" -eq 0 ]; then
    chown josh:josh "$USER_HOME/Desktop/FM_DVR.desktop"
fi

echo "✅ Created desktop shortcut"

echo ""
echo "🎯 Setup Complete!"
echo "=================="
echo ""
echo "📱 Available audio devices:"
aplay -l | grep -E "(card|device)"
echo ""
echo "🎮 How to use:"
echo "  1. Double-click 'FM DVR Radio' on desktop, OR"
echo "  2. Run: ./launch_fm_dvr.sh"
echo "  3. Click PLAY to start radio"
echo "  4. Use EQ button to open equalizer"
echo "  5. Use Output button to toggle between direct/EQ audio"
echo ""
echo "🎛️ Controls:"
echo "  - PLAY/PAUSE: Start/stop radio"
echo "  - LIVE: Jump to live broadcast"
echo "  - CH +/-: Change preset stations"
echo "  - Tune +/-: Fine tune frequency"
echo "  - << 10s / 10s >>: Skip backward/forward"
echo "  - Progress bar: Click to seek"
echo "  - EQ: Open equalizer"
echo "  - Output: Toggle audio device"
echo ""
echo "✅ Your FM DVR system is ready!"
EOF
